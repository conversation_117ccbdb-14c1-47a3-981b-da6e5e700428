{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "test_ComparisonWithStandardDEXPatterns", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_EconomicImpactAssessment", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_FunctionSignaturesLackDeadlineParameter", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_VulnerabilityConfirmationSummary", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "753:7122:34:-:0;;;;;;;3166:4:12;753:7122:34;;3166:4:12;753:7122:34;;;3166:4:12;753:7122:34;3166:4:12;753:7122:34;;1087:4:23;753:7122:34;;;1087:4:23;753:7122:34;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "753:7122:34:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;753:7122:34;-1:-1:-1;;;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;3204:14;;:::i;:::-;753:7122;;;;;2296:42:25;;;;;;;;753:7122:34;;;2296:42:25;;;753:7122:34;2296:42:25;;;;;;;:::i;:::-;358:279;;131:42;358:279;;;753:7122:34;;;;;2296:42:25;;;;;;;;753:7122:34;;;2296:42:25;;;753:7122:34;2296:42:25;;;;;;;:::i;:::-;358:279;;131:42;358:279;;;753:7122:34;;;;;2296:42:25;;;;;;;;753:7122:34;;;2296:42:25;;;753:7122:34;2296:42:25;;;;;;;:::i;:::-;358:279;;131:42;358:279;;;753:7122:34;;;;;;;;;;;;;;;;;;;;1065:26:23;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;2723:18:16;753:7122:34;;;;;;;2723:18:16;753:7122:34;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;6944:56:34;753:7122;;;;;;;;;;;;;;;-1:-1:-1;;;753:7122:34;;;;6944:56;-1:-1:-1;;;;;;;;;;;6944:56:34;753:7122;;;;;;;;;;;;;;;;;;;;7015:77;-1:-1:-1;;;;;;;;;;;6944:56:34;753:7122;;;;;;;;;;;;;;;;;;;;7107:78;-1:-1:-1;;;;;;;;;;;7200:79:34;753:7122;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;6944:56:34;753:7122;;;7200:79;-1:-1:-1;;;;;;;;;;;6944:56:34;753:7122;;;;;;;;;;;;;;;;;;;;7294:77;-1:-1:-1;;;;;;;;;;;6944:56:34;753:7122;;;;;;;;;;;;;;;-1:-1:-1;;;753:7122:34;;;;7386:64;-1:-1:-1;;;;;;;;;;;6944:56:34;753:7122;;;;;;;;;;;;;;;;;;;;7465:72;-1:-1:-1;;;;;;;;;;;753:7122:34;;;;;;;;;;;7552:14;-1:-1:-1;;;;;;;;;;;7200:79:34;753:7122;;;;;;;;;;;;;;;;;;;;;6944:56;753:7122;;;7581:101;-1:-1:-1;;;753:7122:34;;;;6944:56;753:7122;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;2575:18:16;753:7122:34;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;2575:18:16;753:7122:34;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;2876:18:16;753:7122:34;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;2876:18:16;753:7122:34;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;753:7122:34;;;;-1:-1:-1;;;753:7122:34;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;5891:45:34;753:7122;;;;;;;;;;-1:-1:-1;;;753:7122:34;;;;5123:6;753:7122;;;;5891:45;-1:-1:-1;;;;;;;;;;;5891:45:34;753:7122;;;;;;;;;;;;;;;5177:4;753:7122;;;;5951:59;-1:-1:-1;;;;;;;;;;;6025:82:34;753:7122;;;;;;;;;;;;;;;-1:-1:-1;;;5891:45:34;753:7122;;;;;;;;6025:82;-1:-1:-1;;;;;;;;;;;5891:45:34;753:7122;;;;;;;;;;-1:-1:-1;;;753:7122:34;;;;;;;;;6122:48;-1:-1:-1;;;;;;;;;;;5891:45:34;753:7122;;;;;;;;;;-1:-1:-1;;;753:7122:34;;;;;;;;;6185:50;-1:-1:-1;;;;;;;;;;;6025:82:34;753:7122;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;5891:45:34;753:7122;;;6250:91;753:7122;;;;;;;;;;;-1:-1:-1;;753:7122:34;;;;3653:18:16;753:7122:34;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;3653:18:16;753:7122:34;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;753:7122:34;;;;3162:18:16;753:7122:34;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;3162:18:16;753:7122:34;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;-1:-1:-1;;753:7122:34;;;;3346:26:16;753:7122:34;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;3346:26:16;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;753:7122:34;;;;;;;;3501:18:16;753:7122:34;;;;;;;3501:18:16;753:7122:34;;;;;;;;;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;753:7122:34;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;753:7122:34;;;;;;;;3794:16:16;753:7122:34;;;;;;;3794:16:16;753:7122:34;;;;;;;;;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;753:7122:34;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;753:7122:34;;;;-1:-1:-1;;;753:7122:34;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;2296:42:25;;;;;;;;753:7122:34;;;2296:42:25;;;753:7122:34;2296:42:25;;;;;;;:::i;:::-;358:279;;131:42;358:279;;;4745:17:34;;:::i;:::-;-1:-1:-1;;;;;;;;;;;753:7122:34;;;;;;;;;;;;;;;;;;;;;4778:76;753:7122;;;;;;;-1:-1:-1;;753:7122:34;;;;3948:19:16;753:7122:34;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;3948:19:16;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;753:7122:34;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;-1:-1:-1;;753:7122:34;;;;3018:16:16;753:7122:34;;;;;;;;3018:16:16;753:7122:34;;;;;;;;;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;753:7122:34;;;;;;;;-1:-1:-1;;753:7122:34;;;;:::o;:::-;;;;;;;;;;;;;;-1:-1:-1;753:7122:34;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;753:7122:34;;;;;-1:-1:-1;753:7122:34;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;-1:-1:-1;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:::o;:::-;;;-1:-1:-1;753:7122:34;;;;;-1:-1:-1;753:7122:34;;-1:-1:-1;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;753:7122:34;;;;;-1:-1:-1;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;753:7122:34;;-1:-1:-1;753:7122:34;;-1:-1:-1;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;;753:7122:34;;;;;;;;;;;;;-1:-1:-1;;;;;;753:7122:34;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;753:7122:34;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;753:7122:34;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;753:7122:34;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;753:7122:34;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;753:7122:34;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;753:7122:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1306:195:11;1365:7;753:7122:34;;;;;;1395:4:11;1388:11;:::o;1361:134::-;753:7122:34;;;;;1437:33:11;;753:7122:34;1437:33:11;;;753:7122:34;192:59:11;;;1255:17;;;753:7122:34;1255:17:11;1437:33;;;753:7122:34;1437:33:11;;;;;;;753:7122:34;1437:33:11;;;1361:134;1437:47;;;1430:54;:::o;1437:33::-;;;1255:17;1437:33;;1255:17;1437:33;;;;;;1255:17;1437:33;;;:::i;:::-;;;1255:17;;;;;1437:33;;;;;;-1:-1:-1;1437:33:11;;;753:7122:34;;1255:17:11;753:7122:34;1255:17:11;;;;;2226:120:25;-1:-1:-1;753:7122:34;;;2296:42:25;;;;;;;;;4390:44:34;;;2296:42:25;;;753:7122:34;2296:42:25;;;;;;;:::i;:::-;358:279;;131:42;358:279;;;2226:120::o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "test_ComparisonWithStandardDEXPatterns()": "34a45b39", "test_EconomicImpactAssessment()": "966482aa", "test_FunctionSignaturesLackDeadlineParameter()": "fc20cf57", "test_VulnerabilityConfirmationSummary()": "cb235eeb"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_ComparisonWithStandardDEXPatterns\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_EconomicImpactAssessment\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_FunctionSignaturesLackDeadlineParameter\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_VulnerabilityConfirmationSummary\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Simple Proof of Concept test to verify the alleged bug in Issue.md regarding missing deadline protection  Bug Claims from Issue.md: 1. placeAndExecuteMarketBuy/Sell use _minAmountOut but no deadline 2. Sandwichers/relays can delay transactions until price moves against the taker 3. This creates MEV/\\\"held tx\\\" risk  Fix: add uint256 deadline param and require(block.timestamp <= deadline, \\\"expired\\\")\",\"kind\":\"dev\",\"methods\":{\"test_ComparisonWithStandardDEXPatterns()\":{\"details\":\"Test Case 4: Comparison with Standard DEX Patterns This test shows how standard DEX implementations include deadline protection\"},\"test_EconomicImpactAssessment()\":{\"details\":\"Test Case 5: Economic Impact Assessment This test quantifies the potential economic impact of the vulnerability\"},\"test_FunctionSignaturesLackDeadlineParameter()\":{\"details\":\"Test Case 1: Code Analysis - Verify function signatures lack deadline parameter This test analyzes the actual function signatures to confirm the vulnerability\"},\"test_VulnerabilityConfirmationSummary()\":{\"details\":\"Test Case 6: Vulnerability Confirmation Summary This test summarizes all findings and confirms the vulnerability exists\"}},\"title\":\"SimpleDeadlineVulnerabilityPOC\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/SimpleDeadlineVulnerabilityPOC.t.sol\":\"SimpleDeadlineVulnerabilityPOC\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/AbstractAMM.sol\":{\"keccak256\":\"0x174a22f9d3fe82b315d86ca86e1358f57e9b199618439284b0d2386f6a3f536e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://986eeb1def68278dcbdcef380243c6835219a1bfab7cac312957967ae3baf804\",\"dweb:/ipfs/QmT2WWD1jrDH4nkXKmiXWwvMyJMZuSyS5wCGzn9FZ6u4Wg\"]},\"contracts/OrderBook.sol\":{\"keccak256\":\"0x054667b2015d71a16fe0c57816afa53d8a2c8c4a3fdfc603ad3d4c9a08820b08\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://80bc3ca50aa8a391d0a94d72671c8bd88d6a53852cedff355fe3f2eb4324b24f\",\"dweb:/ipfs/QmbQtFxyEQN2C4yBWzbm6JPmtKoENuM7LB51BVpuhsW1Ao\"]},\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/libraries/BitMath.sol\":{\"keccak256\":\"0xc6be48e23a72c9e2ed161a622b4839c68fcc1c3a3ca9c24d0c6e5cac9537541f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b8241e020c9f6f2a21f8c0451f6ecb396055a4bf86db122ae9236c555534130f\",\"dweb:/ipfs/QmQz4ghu9QLk2VixhGDjJCiyhn7a7ZC5UctDk1bQ2WLdXw\"]},\"contracts/libraries/ERC2771Context.sol\":{\"keccak256\":\"0x7458e7a07eb42f479dd6a547733373f21d7a45e0d9a78d545e16db6639e61ef5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8d278e5acd57e1943f2e411bcd6f4c13fb8620ae046cf8c8026fd9a103315497\",\"dweb:/ipfs/QmYWsYRhEV9A7sXgkFX6ES278EMN7b2rJ1JyetQ2XBQrWP\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"contracts/libraries/FixedPointMathLib.sol\":{\"keccak256\":\"0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946\",\"dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N\"]},\"contracts/libraries/OrderLinkedList.sol\":{\"keccak256\":\"0x48132979bf939d9d2b42d112a507300cdf1cd440d06ba98ea86e847e60fd2f32\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0c20ad217ecf6e9ef7892d6e576f26d1fcd21f1804e4e8c1db66a2b7defdb133\",\"dweb:/ipfs/QmXqJ9XNkKJSs8c1pDVoghDWuacooxSCW7UAC4xL52FTjU\"]},\"contracts/libraries/TreeMath.sol\":{\"keccak256\":\"0x61fb4aea5f902aaee0bc1604e5500acee6d426e03bc638afee1d310a24eaba10\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eaaa42df47bc993cd8bc754198423ff87bcaaa2994bdcf9206bfae67fa81aca0\",\"dweb:/ipfs/Qmaqmu7ShkXGFauY6xfMTpnzMUHXbxuJFrCkPCsdMdT9dS\"]},\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3\",\"dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c\",\"dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/solady/src/utils/Initializable.sol\":{\"keccak256\":\"0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559\",\"dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy\"]},\"node_modules/solady/src/utils/SafeTransferLib.sol\":{\"keccak256\":\"0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c\",\"dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J\"]},\"node_modules/solady/src/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16\",\"dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV\"]},\"test/SimpleDeadlineVulnerabilityPOC.t.sol\":{\"keccak256\":\"0x0693c98b28bccffc75b5b5335b38d10c259136ffb402383c41c56172f3154860\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://42993fb016cd3ad301af7906b5643f53e7f4f594561c79a4a20f8156bc6f08c8\",\"dweb:/ipfs/QmRWNx9CrWmL3PSfQHLBosqzQaEPfBZAF6MHUVChK8BoY9\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_ComparisonWithStandardDEXPatterns"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_EconomicImpactAssessment"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_FunctionSignaturesLackDeadlineParameter"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_VulnerabilityConfirmationSummary"}], "devdoc": {"kind": "dev", "methods": {"test_ComparisonWithStandardDEXPatterns()": {"details": "Test Case 4: Comparison with Standard DEX Patterns This test shows how standard DEX implementations include deadline protection"}, "test_EconomicImpactAssessment()": {"details": "Test Case 5: Economic Impact Assessment This test quantifies the potential economic impact of the vulnerability"}, "test_FunctionSignaturesLackDeadlineParameter()": {"details": "Test Case 1: Code Analysis - Verify function signatures lack deadline parameter This test analyzes the actual function signatures to confirm the vulnerability"}, "test_VulnerabilityConfirmationSummary()": {"details": "Test Case 6: Vulnerability Confirmation Summary This test summarizes all findings and confirms the vulnerability exists"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/SimpleDeadlineVulnerabilityPOC.t.sol": "SimpleDeadlineVulnerabilityPOC"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/AbstractAMM.sol": {"keccak256": "0x174a22f9d3fe82b315d86ca86e1358f57e9b199618439284b0d2386f6a3f536e", "urls": ["bzz-raw://986eeb1def68278dcbdcef380243c6835219a1bfab7cac312957967ae3baf804", "dweb:/ipfs/QmT2WWD1jrDH4nkXKmiXWwvMyJMZuSyS5wCGzn9FZ6u4Wg"], "license": "BUSL-1.1"}, "contracts/OrderBook.sol": {"keccak256": "0x054667b2015d71a16fe0c57816afa53d8a2c8c4a3fdfc603ad3d4c9a08820b08", "urls": ["bzz-raw://80bc3ca50aa8a391d0a94d72671c8bd88d6a53852cedff355fe3f2eb4324b24f", "dweb:/ipfs/QmbQtFxyEQN2C4yBWzbm6JPmtKoENuM7LB51BVpuhsW1Ao"], "license": "BUSL-1.1"}, "contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/libraries/BitMath.sol": {"keccak256": "0xc6be48e23a72c9e2ed161a622b4839c68fcc1c3a3ca9c24d0c6e5cac9537541f", "urls": ["bzz-raw://b8241e020c9f6f2a21f8c0451f6ecb396055a4bf86db122ae9236c555534130f", "dweb:/ipfs/QmQz4ghu9QLk2VixhGDjJCiyhn7a7ZC5UctDk1bQ2WLdXw"], "license": "MIT"}, "contracts/libraries/ERC2771Context.sol": {"keccak256": "0x7458e7a07eb42f479dd6a547733373f21d7a45e0d9a78d545e16db6639e61ef5", "urls": ["bzz-raw://8d278e5acd57e1943f2e411bcd6f4c13fb8620ae046cf8c8026fd9a103315497", "dweb:/ipfs/QmYWsYRhEV9A7sXgkFX6ES278EMN7b2rJ1JyetQ2XBQrWP"], "license": "MIT"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "contracts/libraries/FixedPointMathLib.sol": {"keccak256": "0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd", "urls": ["bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946", "dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N"], "license": "MIT"}, "contracts/libraries/OrderLinkedList.sol": {"keccak256": "0x48132979bf939d9d2b42d112a507300cdf1cd440d06ba98ea86e847e60fd2f32", "urls": ["bzz-raw://0c20ad217ecf6e9ef7892d6e576f26d1fcd21f1804e4e8c1db66a2b7defdb133", "dweb:/ipfs/QmXqJ9XNkKJSs8c1pDVoghDWuacooxSCW7UAC4xL52FTjU"], "license": "BUSL-1.1"}, "contracts/libraries/TreeMath.sol": {"keccak256": "0x61fb4aea5f902aaee0bc1604e5500acee6d426e03bc638afee1d310a24eaba10", "urls": ["bzz-raw://eaaa42df47bc993cd8bc754198423ff87bcaaa2994bdcf9206bfae67fa81aca0", "dweb:/ipfs/Qmaqmu7ShkXGFauY6xfMTpnzMUHXbxuJFrCkPCsdMdT9dS"], "license": "MIT"}, "lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43", "urls": ["bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3", "dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01", "urls": ["bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c", "dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/solady/src/utils/Initializable.sol": {"keccak256": "0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec", "urls": ["bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559", "dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy"], "license": "MIT"}, "node_modules/solady/src/utils/SafeTransferLib.sol": {"keccak256": "0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a", "urls": ["bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c", "dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J"], "license": "MIT"}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"keccak256": "0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e", "urls": ["bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16", "dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV"], "license": "MIT"}, "test/SimpleDeadlineVulnerabilityPOC.t.sol": {"keccak256": "0x0693c98b28bccffc75b5b5335b38d10c259136ffb402383c41c56172f3154860", "urls": ["bzz-raw://42993fb016cd3ad301af7906b5643f53e7f4f594561c79a4a20f8156bc6f08c8", "dweb:/ipfs/QmRWNx9CrWmL3PSfQHLBosqzQaEPfBZAF6MHUVChK8BoY9"], "license": "MIT"}}, "version": 1}, "id": 34}