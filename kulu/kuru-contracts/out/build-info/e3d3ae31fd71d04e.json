{"id": "e3d3ae31fd71d04e", "source_id_to_path": {"0": "contracts/AbstractAMM.sol", "1": "contracts/MarginAccount.sol", "2": "contracts/OrderBook.sol", "3": "contracts/Router.sol", "4": "contracts/interfaces/IKuruAMMVault.sol", "5": "contracts/interfaces/IMarginAccount.sol", "6": "contracts/interfaces/IOrderBook.sol", "7": "contracts/interfaces/IRouter.sol", "8": "contracts/libraries/BitMath.sol", "9": "contracts/libraries/ERC2771Context.sol", "10": "contracts/libraries/Errors.sol", "11": "contracts/libraries/FixedPointMathLib.sol", "12": "contracts/libraries/OrderLinkedList.sol", "13": "contracts/libraries/TreeMath.sol", "14": "lib/forge-std/src/Base.sol", "15": "lib/forge-std/src/StdAssertions.sol", "16": "lib/forge-std/src/StdChains.sol", "17": "lib/forge-std/src/StdCheats.sol", "18": "lib/forge-std/src/StdConstants.sol", "19": "lib/forge-std/src/StdError.sol", "20": "lib/forge-std/src/StdInvariant.sol", "21": "lib/forge-std/src/StdJson.sol", "22": "lib/forge-std/src/StdMath.sol", "23": "lib/forge-std/src/StdStorage.sol", "24": "lib/forge-std/src/StdStyle.sol", "25": "lib/forge-std/src/StdToml.sol", "26": "lib/forge-std/src/StdUtils.sol", "27": "lib/forge-std/src/Test.sol", "28": "lib/forge-std/src/Vm.sol", "29": "lib/forge-std/src/console.sol", "30": "lib/forge-std/src/console2.sol", "31": "lib/forge-std/src/interfaces/IMulticall3.sol", "32": "lib/forge-std/src/safeconsole.sol", "33": "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "34": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "35": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "36": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "37": "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "38": "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "39": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "40": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "41": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "42": "lib/openzeppelin-contracts/contracts/utils/Address.sol", "43": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "44": "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "45": "lib/openzeppelin-contracts/contracts/utils/Errors.sol", "46": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "47": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol", "48": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "49": "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol", "50": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "51": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "52": "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol", "53": "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol", "54": "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "55": "node_modules/@openzeppelin/contracts/utils/Context.sol", "56": "node_modules/solady/src/auth/Ownable.sol", "57": "node_modules/solady/src/utils/Initializable.sol", "58": "node_modules/solady/src/utils/SafeTransferLib.sol", "59": "node_modules/solady/src/utils/UUPSUpgradeable.sol", "60": "test/FeeCollectorZeroAddressPOC.t.sol", "61": "test/lib/MintableERC20.sol"}, "language": "Solidity"}