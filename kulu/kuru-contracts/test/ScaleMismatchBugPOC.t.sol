// SPDX-License-Identifier: BUSL-1.1
pragma solidity ^0.8.20;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {<PERSON><PERSON><PERSON>orwarder} from "../contracts/KuruForwarder.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {Router} from "../contracts/Router.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import {ERC1967Proxy} from "openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "openzeppelin-contracts/contracts/utils/Create2.sol";

/**
 * @title ScaleMismatchBugPOC
 * @dev Proof of Concept test to verify the scale mismatch bug in KuruForwarder.executePriceDependent()
 * 
 * CRITICAL BUG: Scale Mismatch Between Price Sources
 * 
 * Issue: KuruForwarder.executePriceDependent() compares:
 * - req.price (user-provided trigger price, expected in price precision 1e2)
 * - _currentBidPrice from IOrderBook(req.market).bestBidAsk() (returns vault precision 1e18)
 * 
 * This creates a massive scale difference (16 orders of magnitude) that makes
 * price comparisons meaningless regardless of the logic direction.
 * 
 * Impact: 
 * - User trigger prices are effectively ignored
 * - Price-dependent transactions execute at completely wrong price levels
 * - Severe financial losses for users
 */
contract ScaleMismatchBugPOC is Test {
    uint96 constant SIZE_PRECISION = 10 ** 10;
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint96 constant SPREAD = 30;

    KuruForwarder kuruForwarder;
    Router router;
    MarginAccount marginAccount;
    OrderBook orderBook;
    MintableERC20 tokenA;
    MintableERC20 tokenB;
    address trustedForwarder;

    function setUp() public {
        // Create test tokens
        tokenA = new MintableERC20("TokenA", "TKA");
        tokenB = new MintableERC20("TokenB", "TKB");
        
        // Deploy contracts
        // Deploy KuruForwarder
        KuruForwarder kuruForwarderImplementation = new KuruForwarder();
        address kuruForwarderProxy = address(new ERC1967Proxy(address(kuruForwarderImplementation), ""));
        kuruForwarder = KuruForwarder(kuruForwarderProxy);
        bytes4[] memory allowedInterfaces = new bytes4[](1);
        allowedInterfaces[0] = bytes4(keccak256("placeAndExecuteMarketBuy(uint96,uint256,bool,bool)"));
        kuruForwarder.initialize(address(this), allowedInterfaces);
        
        // Set trusted forwarder to KuruForwarder
        trustedForwarder = address(kuruForwarder);
        
        // Deploy Router
        Router routerImplementation = new Router();
        address routerProxy = Create2.deploy(
            0,
            bytes32(keccak256("")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(routerImplementation, bytes("")))
        );
        router = Router(payable(routerProxy));
        
        // Deploy MarginAccount
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), trustedForwarder);
        
        // Initialize Router
        OrderBook orderBookImplementation = new OrderBook();
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        router.initialize(address(this), address(marginAccount), address(orderBookImplementation), address(kuruAmmVaultImplementation), trustedForwarder);
        
        // Deploy OrderBook market
        uint32 tickSize = PRICE_PRECISION / 2;
        uint96 minSize = 2 * 10 ** 8;
        uint96 maxSize = 10 ** 12;
        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            address(tokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            tickSize,
            minSize,
            maxSize,
            0, // takerFeeBps
            0, // makerFeeBps
            SPREAD
        );
        
        orderBook = OrderBook(deployedMarket);
        
        // Setup some liquidity to establish prices
        _setupLiquidity();
    }

    function _setupLiquidity() internal {
        // Add some buy and sell orders to establish bid/ask prices
        address maker1 = address(0x1001);
        address maker2 = address(0x1002);
        
        // Calculate tick-aligned prices (tickSize = PRICE_PRECISION / 2 = 50)
        uint32 sellPrice1 = 200; // 200 % 50 = 0, valid
        uint32 sellPrice2 = 250; // 250 % 50 = 0, valid  
        uint32 buyPrice1 = 150;  // 150 % 50 = 0, valid
        uint32 buyPrice2 = 100;  // 100 % 50 = 0, valid
        
        // Setup maker1 (sell orders)
        // For sell orders: need (size * 10^decimals / SIZE_PRECISION) base tokens
        uint256 sellOrderSize = 10 * SIZE_PRECISION;
        uint256 baseAmount = sellOrderSize * 10**tokenA.decimals() / SIZE_PRECISION;
        uint256 totalBaseAmount = baseAmount * 2 + 100 * 10**tokenA.decimals(); // For 2 orders + buffer
        
        // Mint the exact amount needed
        tokenA.mint(maker1, totalBaseAmount);
        
        vm.startPrank(maker1);
        tokenA.approve(address(marginAccount), totalBaseAmount);
        marginAccount.deposit(maker1, address(tokenA), totalBaseAmount);
        orderBook.addSellOrder(sellPrice1, uint96(sellOrderSize), false); // Sell at price 200
        orderBook.addSellOrder(sellPrice2, uint96(sellOrderSize), false); // Sell at price 250
        vm.stopPrank();
        
        // Setup maker2 (buy orders) - calculate required quote tokens properly
        // Calculate required amounts for buy orders first
        // For buy order: need (price * size / SIZE_PRECISION) * 10^decimals / PRICE_PRECISION quote tokens
        uint256 orderSize = 10 * SIZE_PRECISION;
        uint256 amount1 = (uint256(buyPrice1) * orderSize / SIZE_PRECISION) * 10**tokenB.decimals() / PRICE_PRECISION;
        uint256 amount2 = (uint256(buyPrice2) * orderSize / SIZE_PRECISION) * 10**tokenB.decimals() / PRICE_PRECISION;
        uint256 totalAmount = amount1 + amount2 + 1000 * 10**tokenB.decimals(); // Extra buffer
        
        // Mint the exact amount needed (this was missing!)
        tokenB.mint(maker2, totalAmount);
        
        vm.startPrank(maker2);
        tokenB.approve(address(marginAccount), totalAmount);
        marginAccount.deposit(maker2, address(tokenB), totalAmount);
        orderBook.addBuyOrder(buyPrice1, uint96(orderSize), false); // Buy at price 150
        orderBook.addBuyOrder(buyPrice2, uint96(orderSize), false); // Buy at price 100
        vm.stopPrank();
    }

    /**
     * @dev Test Case 1: Demonstrate the scale mismatch between price sources
     */
    function test_ScaleMismatchDemonstration() public {
        (uint256 currentBid, uint256 currentAsk) = orderBook.bestBidAsk();
        
        console.log("=== SCALE MISMATCH BUG DEMONSTRATION ===");
        console.log("");
        console.log("1. OrderBook.bestBidAsk() returns VAULT PRECISION (1e18):");
        console.log("   Current bid:", currentBid);
        console.log("   Current ask:", currentAsk);
        console.log("");
        
        // Show what users would expect to use as trigger prices (price precision)
        uint256 expectedTriggerPrice = 175; // User wants to trigger at price 175 (in price precision)
        console.log("2. User trigger prices are in PRICE PRECISION (1e2):");
        console.log("   Example trigger price:", expectedTriggerPrice);
        console.log("");
        
        // Calculate the scale difference
        uint256 scaleDifference = currentBid / expectedTriggerPrice;
        console.log("3. Scale difference:");
        console.log("   currentBid / triggerPrice =", scaleDifference);
        console.log("   This is", scaleDifference / 1e15, "quadrillion times larger!");
        console.log("");
        
        // Show the broken comparison
        console.log("4. KuruForwarder comparison (BROKEN):");
        console.log("   Comparing", expectedTriggerPrice, "< ", currentBid);
        console.log("   Result:", expectedTriggerPrice < currentBid, "(always true due to scale)");
        console.log("");
        
        // Show what the comparison should be
        uint256 currentBidInPricePrecision = currentBid * PRICE_PRECISION / (10**18);
        console.log("5. Correct comparison (after scale conversion):");
        console.log("   Current bid in price precision:", currentBidInPricePrecision);
        console.log("   Comparing", expectedTriggerPrice, "< ", currentBidInPricePrecision);
        console.log("   Result:", expectedTriggerPrice < currentBidInPricePrecision);
        console.log("");
        
        // Assertions to prove the bug
        assertTrue(expectedTriggerPrice < currentBid, "Scale mismatch: tiny trigger always < huge current price");
        assertTrue(scaleDifference > 1e15, "Scale difference is massive (>1 quadrillion)");
        
        console.log("BUG CONFIRMED: Scale mismatch makes price comparisons meaningless!");
    }

    /**
     * @dev Test Case 2: Show impact on different trigger price ranges
     */
    function test_ScaleMismatchImpactOnTriggerRanges() public {
        (uint256 currentBid,) = orderBook.bestBidAsk();
        
        console.log("=== IMPACT ON DIFFERENT TRIGGER PRICE RANGES ===");
        console.log("Current bid (vault precision):", currentBid);
        console.log("");
        
        // Test various trigger price ranges that users might use
        uint256[5] memory triggerPrices = [uint256(1), 50, 100, 500, 1000];
        
        for (uint i = 0; i < triggerPrices.length; i++) {
            uint256 trigger = triggerPrices[i];
            bool comparison = trigger < currentBid;
            
            console.log("Trigger price:", trigger);
            console.log("  trigger < currentBid:", comparison, "(always true)");
            console.log("  Scale ratio:", currentBid / trigger);
            
            // All trigger prices in reasonable ranges will always be < currentBid
            assertTrue(comparison, "All reasonable trigger prices are dwarfed by vault precision");
        }
        
        console.log("");
        console.log("RESULT: User trigger prices are completely ignored!");
        console.log("Price-dependent transactions will execute at wrong price levels.");
    }

    /**
     * @dev Test Case 3: Demonstrate the fix needed
     */
    function test_ProposedFix() public {
        (uint256 currentBid,) = orderBook.bestBidAsk();
        uint256 triggerPrice = 175; // User wants to trigger at 175 (price precision)
        
        console.log("=== PROPOSED FIX DEMONSTRATION ===");
        console.log("Current bid (vault precision):", currentBid);
        console.log("Trigger price (price precision):", triggerPrice);
        console.log("");
        
        // Current broken comparison
        bool brokenComparison = triggerPrice < currentBid;
        console.log("1. Current broken comparison:");
        console.log("   triggerPrice < currentBid:", brokenComparison);
        console.log("");
        
        // Fix option 1: Convert current price to price precision
        uint256 currentBidInPricePrecision = currentBid * PRICE_PRECISION / (10**18);
        bool fix1 = triggerPrice < currentBidInPricePrecision;
        console.log("2. Fix option 1 - Convert current to price precision:");
        console.log("   currentBid in price precision:", currentBidInPricePrecision);
        console.log("   triggerPrice < currentBidInPricePrecision:", fix1);
        console.log("");
        
        // Fix option 2: Convert trigger price to vault precision
        uint256 triggerPriceInVaultPrecision = triggerPrice * (10**18) / PRICE_PRECISION;
        bool fix2 = triggerPriceInVaultPrecision < currentBid;
        console.log("3. Fix option 2 - Convert trigger to vault precision:");
        console.log("   triggerPrice in vault precision:", triggerPriceInVaultPrecision);
        console.log("   triggerPriceInVaultPrecision < currentBid:", fix2);
        console.log("");
        
        // Both fixes should give the same result
        assertEq(fix1, fix2, "Both fix approaches should give same result");
        assertTrue(fix1 != brokenComparison, "Fixed comparison differs from broken one");
        
        console.log("SOLUTION: Convert prices to same precision before comparison!");
    }
}
