// SPDX-License-Identifier: BUSL-1.1
pragma solidity ^0.8.20;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {<PERSON><PERSON><PERSON>orwarder} from "../contracts/KuruForwarder.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {Router} from "../contracts/Router.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import {ERC1967Proxy} from "openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "openzeppelin-contracts/contracts/utils/Create2.sol";

/**
 * @title PriceTriggerBugPOC

 * 
 * Bug Claims:
 * 1. Price trigger logic is inverted in KuruForwarder.executePriceDependent()
 * 2. When isBelowPrice=true, it executes when trigger < current (wrong side)
 * 3. When isBelowPrice=false, it executes when trigger > current (wrong side)
 * 4. Uses strict comparison (< / >) excluding equality
 * 
 * Expected behavior:
 * - isBelowPrice=true should execute when current <= trigger
 * - isBelowPrice=false should execute when current >= trigger
 */
contract PriceTriggerBugPOC is Test {
    uint96 constant SIZE_PRECISION = 10 ** 10;
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint96 constant SPREAD = 30;

    KuruForwarder kuruForwarder;
    Router router;
    MarginAccount marginAccount;
    OrderBook orderBook;
    MintableERC20 tokenA;
    MintableERC20 tokenB;
    address trustedForwarder;
    
    address user;
    uint256 userPrivateKey;

    // EIP-712 domain and types for signing
    bytes32 private constant DOMAIN_TYPEHASH = keccak256("EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)");
    bytes32 private constant PRICE_DEPENDENT_TYPEHASH = keccak256("PriceDependentRequest(address from,address market,uint256 price,uint256 value,uint256 nonce,uint256 deadline,bool isBelowPrice,bytes4 selector,bytes data)");

    function setUp() public {
        // Create test tokens
        tokenA = new MintableERC20("TokenA", "TKA");
        tokenB = new MintableERC20("TokenB", "TKB");
        
        // Create user account
        userPrivateKey = 0x1234;
        user = vm.addr(userPrivateKey);
        
        // Deploy contracts
        
        // Deploy Router
        Router routerImplementation = new Router();
        address routerProxy = Create2.deploy(
            0,
            bytes32(keccak256("")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(routerImplementation, bytes("")))
        );
        router = Router(payable(routerProxy));
        
        // Deploy MarginAccount
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), trustedForwarder);
        
        // Deploy KuruForwarder
        KuruForwarder kuruForwarderImplementation = new KuruForwarder();
        address kuruForwarderProxy = address(new ERC1967Proxy(address(kuruForwarderImplementation), ""));
        kuruForwarder = KuruForwarder(kuruForwarderProxy);
        bytes4[] memory allowedInterfaces = new bytes4[](1);
        allowedInterfaces[0] = bytes4(keccak256("placeAndExecuteMarketBuy(uint96,uint256,bool,bool)"));
        kuruForwarder.initialize(address(this), allowedInterfaces);

        // Set trusted forwarder to KuruForwarder
        trustedForwarder = address(kuruForwarder);

        // Initialize Router
        OrderBook orderBookImplementation = new OrderBook();
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        router.initialize(address(this), address(marginAccount), address(orderBookImplementation), address(kuruAmmVaultImplementation), trustedForwarder);
        
        // Deploy OrderBook market
        uint32 tickSize = PRICE_PRECISION / 2;
        uint96 minSize = 2 * 10 ** 8;
        uint96 maxSize = 10 ** 12;
        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            address(tokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            tickSize,
            minSize,
            maxSize,
            0, // takerFeeBps
            0, // makerFeeBps
            SPREAD
        );
        
        orderBook = OrderBook(deployedMarket);
        
        // Setup some liquidity in the orderbook to establish prices
        _setupLiquidity();
    }

    function _setupLiquidity() internal {
        // Add some buy and sell orders to establish bid/ask prices
        address maker1 = address(0x1001);
        address maker2 = address(0x1002);

        // Tokens will be minted as needed for each maker

        // Calculate tick-aligned prices (tickSize = PRICE_PRECISION / 2 = 50)
        uint32 tickSize = PRICE_PRECISION / 2; // 50
        uint32 sellPrice1 = 200; // 200 % 50 = 0, valid
        uint32 sellPrice2 = 250; // 250 % 50 = 0, valid
        uint32 buyPrice1 = 150;  // 150 % 50 = 0, valid
        uint32 buyPrice2 = 100;  // 100 % 50 = 0, valid

        // Setup maker1 (sell orders)
        // For sell orders: need (size * 10^decimals / SIZE_PRECISION) base tokens
        uint256 sellOrderSize = 10 * SIZE_PRECISION;
        uint256 baseAmount = sellOrderSize * 10**tokenA.decimals() / SIZE_PRECISION;
        uint256 totalBaseAmount = baseAmount * 2 + 100 * 10**tokenA.decimals(); // For 2 orders + buffer

        // Mint the exact amount needed
        tokenA.mint(maker1, totalBaseAmount);

        vm.startPrank(maker1);
        tokenA.approve(address(marginAccount), totalBaseAmount);
        marginAccount.deposit(maker1, address(tokenA), totalBaseAmount);
        orderBook.addSellOrder(sellPrice1, uint96(sellOrderSize), false); // Sell at price 200
        orderBook.addSellOrder(sellPrice2, uint96(sellOrderSize), false); // Sell at price 250
        vm.stopPrank();

        // Setup maker2 (buy orders) - calculate required quote tokens properly
        // Calculate required amounts for buy orders first
        // For buy order: need (price * size / SIZE_PRECISION) * 10^decimals / PRICE_PRECISION quote tokens
        uint256 orderSize = 10 * SIZE_PRECISION;
        uint256 amount1 = (uint256(buyPrice1) * orderSize / SIZE_PRECISION) * 10**tokenB.decimals() / PRICE_PRECISION;
        uint256 amount2 = (uint256(buyPrice2) * orderSize / SIZE_PRECISION) * 10**tokenB.decimals() / PRICE_PRECISION;
        uint256 totalAmount = amount1 + amount2 + 1000 * 10**tokenB.decimals(); // Extra buffer

        // Mint the exact amount needed (this was missing!)
        tokenB.mint(maker2, totalAmount);

        vm.startPrank(maker2);
        tokenB.approve(address(marginAccount), totalAmount);
        marginAccount.deposit(maker2, address(tokenB), totalAmount);
        orderBook.addBuyOrder(buyPrice1, uint96(orderSize), false); // Buy at price 150
        orderBook.addBuyOrder(buyPrice2, uint96(orderSize), false); // Buy at price 100
        vm.stopPrank();
    }

    function _createPriceDependentRequest(
        uint256 triggerPrice,
        bool isBelowPrice,
        uint256 nonce
    ) internal view returns (KuruForwarder.PriceDependentRequest memory, bytes memory) {
        // Create function call data for placeAndExecuteMarketBuy
        bytes memory data = abi.encode(
            uint96(1000), // size: 1000 units
            uint256(0),   // minAmountOut: 0
            false,        // isMargin: false
            true          // isFillOrKill: true
        );
        
        KuruForwarder.PriceDependentRequest memory request = KuruForwarder.PriceDependentRequest({
            from: user,
            market: address(orderBook),
            price: triggerPrice,
            value: 0,
            nonce: nonce,
            deadline: block.timestamp + 1 hours,
            isBelowPrice: isBelowPrice,
            selector: bytes4(keccak256("placeAndExecuteMarketBuy(uint96,uint256,bool,bool)")),
            data: data
        });
        
        // Sign the request
        bytes32 structHash = keccak256(abi.encode(
            PRICE_DEPENDENT_TYPEHASH,
            request.from,
            request.market,
            request.price,
            request.value,
            request.nonce,
            request.deadline,
            request.isBelowPrice,
            request.selector,
            keccak256(request.data)
        ));
        
        bytes32 domainSeparator = keccak256(abi.encode(
            DOMAIN_TYPEHASH,
            keccak256("KuruForwarder"),
            keccak256("1.0.0"),
            block.chainid,
            address(kuruForwarder)
        ));
        
        bytes32 digest = keccak256(abi.encodePacked("\x19\x01", domainSeparator, structHash));
        (uint8 v, bytes32 r, bytes32 s) = vm.sign(userPrivateKey, digest);
        bytes memory signature = abi.encodePacked(r, s, v);
        
        return (request, signature);
    }

    /**
     * @dev Test Case 1: Demonstrate the bug - isBelowPrice=true executes on wrong side
     *
     * Scenario: User wants to execute when price drops to a very low value (isBelowPrice=true)
     * Current bid price: ~1.5e18 (vault precision)
     * Trigger price: 100 (price precision)
     *
     * Bug: Current code checks (100 < 1.5e18) which is true, so it tries to execute
     * Correct: Should NOT execute because current price >> trigger (even accounting for scale)
     */
    function test_BugDemonstrated_IsBelowPriceExecutesOnWrongSide() public {
        (uint256 currentBid, uint256 currentAsk) = orderBook.bestBidAsk();
        console.log("Current bid (vault precision):", currentBid);
        console.log("Current ask (vault precision):", currentAsk);

        uint256 triggerPrice = 100; // Very low trigger price
        bool isBelowPrice = true;   // Execute when price is below trigger

        // Convert current bid to price precision for comparison
        uint256 currentBidInPricePrecision = currentBid * PRICE_PRECISION / (10**18);
        console.log("Current bid (price precision):", currentBidInPricePrecision);
        console.log("Trigger price:", triggerPrice);

        // Demonstrate the buggy logic
        bool buggyLogic = (isBelowPrice && triggerPrice < currentBid);
        bool correctLogic = (isBelowPrice && currentBidInPricePrecision <= triggerPrice);

        console.log("Buggy logic result (trigger < currentBid):", buggyLogic);
        console.log("Correct logic result (currentBid <= trigger):", correctLogic);

        // The bug: buggy logic returns true when it should return false
        assertTrue(buggyLogic, "Buggy logic should return true (demonstrating the bug)");
        assertFalse(correctLogic, "Correct logic should return false");

        // This proves the logic is inverted
        assertTrue(buggyLogic != correctLogic, "BUG CONFIRMED: Logic is inverted");
    }

    /**
     * @dev Test Case 2: Demonstrate the bug - isBelowPrice=false executes on wrong side
     *
     * Scenario: User wants to execute when price rises to a very high value (isBelowPrice=false)
     * Current bid price: ~1.5e18 (vault precision)
     * Trigger price: 3000000000000000000 (3e18, higher than current)
     *
     * Bug: Current code checks (3e18 > 1.5e18) which is true, so it tries to execute
     * Correct: Should NOT execute because we want to wait for price to rise above trigger
     */
    function test_BugDemonstrated_IsAbovePriceExecutesOnWrongSide() public {
        (uint256 currentBid, uint256 currentAsk) = orderBook.bestBidAsk();
        console.log("Current bid (vault precision):", currentBid);
        console.log("Current ask (vault precision):", currentAsk);

        // Use a trigger price higher than current bid (in vault precision)
        uint256 triggerPrice = 3000000000000000000; // 3e18, higher than current ~1.5e18
        bool isBelowPrice = false;  // Execute when price is above trigger

        console.log("Trigger price (vault precision):", triggerPrice);

        // Demonstrate the buggy logic
        bool buggyLogic = (!isBelowPrice && triggerPrice > currentBid);
        bool correctLogic = (!isBelowPrice && currentBid >= triggerPrice);

        console.log("Buggy logic result (trigger > currentBid):", buggyLogic);
        console.log("Correct logic result (currentBid >= trigger):", correctLogic);

        // The bug: buggy logic returns true when it should return false
        assertTrue(buggyLogic, "Buggy logic should return true (demonstrating the bug)");
        assertFalse(correctLogic, "Correct logic should return false");

        // This proves the logic is inverted
        assertTrue(buggyLogic != correctLogic, "BUG CONFIRMED: Logic is inverted");
    }

    /**
     * @dev Test Case 3: Demonstrate scale mismatch issue
     *
     * This test reveals that bestBidAsk() returns vault precision (1e18)
     * but trigger prices are expected in price precision (1e2)
     */
    function test_ScaleMismatchIssue() public {
        (uint256 currentBid, uint256 currentAsk) = orderBook.bestBidAsk();

        console.log("=== SCALE MISMATCH ISSUE ===");
        console.log("bestBidAsk() returns vault precision (1e18):");
        console.log("  Current bid:", currentBid);
        console.log("  Current ask:", currentAsk);

        // Convert to price precision for proper comparison
        uint256 currentBidInPricePrecision = currentBid * PRICE_PRECISION / (10**18);
        uint256 currentAskInPricePrecision = currentAsk * PRICE_PRECISION / (10**18);

        console.log("Converted to price precision (1e2):");
        console.log("  Current bid:", currentBidInPricePrecision);
        console.log("  Current ask:", currentAskInPricePrecision);

        // Demonstrate the scale difference
        uint256 scaleDifference = 10**18 / PRICE_PRECISION;
        console.log("Scale difference (vault/price):", scaleDifference);

        // This shows the prices are in different scales
        assertTrue(currentBid > 1000000, "Vault precision values are much larger");
        assertTrue(currentBidInPricePrecision < 1000, "Price precision values are much smaller");

        console.log("ISSUE: KuruForwarder compares these different scales directly!");
        console.log("This causes incorrect trigger evaluations regardless of logic direction.");
    }

    /**
     * @dev Test Case 4: Verify equality edge case is missed
     *
     * The current code uses strict comparison (< / >) which excludes equality
     * This means triggers at exactly the current price never fire
     */
    function test_EqualityEdgeCaseMissed() public {
        (uint256 currentBid,) = orderBook.bestBidAsk();

        // Test the equality case with the buggy logic
        bool isBelowPrice = true;

        // Buggy logic for equality case: (isBelowPrice && triggerPrice < currentBid)
        // When triggerPrice == currentBid: (true && false) = false
        bool buggyLogicAtEquality = (isBelowPrice && currentBid < currentBid); // Always false

        // Correct logic should be: (isBelowPrice && currentBid <= triggerPrice)
        // When triggerPrice == currentBid: (true && true) = true
        bool correctLogicAtEquality = (isBelowPrice && currentBid <= currentBid); // Always true

        console.log("Equality edge case demonstration:");
        console.log("Current bid:", currentBid);
        console.log("When trigger == current bid:");
        console.log("  Buggy logic result:", buggyLogicAtEquality);
        console.log("  Correct logic result:", correctLogicAtEquality);

        // Demonstrate the strict comparison issue
        assertFalse(buggyLogicAtEquality, "Buggy logic excludes equality (strict <)");
        assertTrue(correctLogicAtEquality, "Correct logic includes equality (<=)");

        console.log("BUG CONFIRMED: Strict comparison misses equality edge case");
    }

    /**
     * @dev Test Case 5: Show the actual buggy logic in action
     *
     * This test directly calls the problematic require statement logic
     */
    function test_BuggyLogicDemonstration() public {
        (uint256 currentBid,) = orderBook.bestBidAsk();

        // Example from Issue.md: User wants execute when price ≤ 100 (isBelowPrice=true)
        // Current bid is 180, trigger is 100
        uint256 triggerPrice = 100;
        bool isBelowPrice = true;

        // Current buggy logic: (req.isBelowPrice && req.price < _currentBidPrice)
        bool buggyResult = (isBelowPrice && triggerPrice < currentBid);

        // This evaluates to: (true && 100 < 180) = true
        // So the transaction executes, which is wrong!
        assertTrue(buggyResult, "Buggy logic incorrectly returns true");

        // Correct logic should be: (isBelowPrice && _currentBidPrice <= req.price)
        bool correctResult = (isBelowPrice && currentBid <= triggerPrice);

        // This evaluates to: (true && 180 <= 100) = false
        // So the transaction should NOT execute, which is correct
        assertFalse(correctResult, "Correct logic correctly returns false");

        console.log("Buggy logic demonstration:");
        console.log("Current bid:", currentBid, "Trigger:", triggerPrice);
        console.log("Buggy logic (trigger < current):", buggyResult);
        console.log("Correct logic (current <= trigger):", correctResult);
    }

    /**
     * @dev Test Case 6: Comprehensive scenario testing
     *
     * Test multiple scenarios to show the pattern of incorrect behavior
     */
    function test_ComprehensiveScenarios() public {
        (uint256 currentBid, uint256 currentAsk) = orderBook.bestBidAsk();

        console.log("=== COMPREHENSIVE SCENARIO TESTING ===");
        console.log("Current bid:", currentBid, "Current ask:", currentAsk);

        // Scenario matrix
        uint256[4] memory triggers = [uint256(100), 150, 200, 250];
        bool[2] memory belowFlags = [true, false];

        for (uint i = 0; i < triggers.length; i++) {
            for (uint j = 0; j < belowFlags.length; j++) {
                uint256 trigger = triggers[i];
                bool isBelow = belowFlags[j];

                // Current buggy logic
                bool buggyLogic = isBelow
                    ? (trigger < currentBid)
                    : (trigger > currentBid);

                // Correct logic
                bool correctLogic = isBelow
                    ? (currentBid <= trigger)
                    : (currentBid >= trigger);

                console.log("Trigger:", trigger, "isBelowPrice:", isBelow);
                console.log("  Buggy result:", buggyLogic, "Correct result:", correctLogic);

                if (buggyLogic != correctLogic) {
                    console.log("  *** MISMATCH DETECTED ***");
                }
            }
        }
    }
}
