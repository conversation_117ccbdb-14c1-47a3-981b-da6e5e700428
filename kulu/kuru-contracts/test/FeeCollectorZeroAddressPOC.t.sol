// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import {MarginAccountErrors} from "../contracts/libraries/Errors.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {Router} from "../contracts/Router.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";

/**
 * @title FeeCollectorZeroAddressPOC
 * @dev Proof of Concept test to verify the alleged bug in Issue.md regarding feeCollector
 * 
 * Bug Claims:
 * 1. feeCollector function can be set to zero address
 * 2. Fees will accrue to an unclaimable account (funds effectively stuck)
 * 
 * This POC will test:
 * - Whether setFeeCollector allows zero address
 * - Whether creditFee function works with zero address feeCollector
 * - Whether fees credited to zero address are truly unclaimable
 * - Impact on the system when feeCollector is zero address
 */
contract FeeCollectorZeroAddressPOC is Test {
    MarginAccount marginAccount;
    Router router;
    OrderBook orderBook;
    MintableERC20 tokenA;
    MintableERC20 tokenB;
    
    address owner;
    address user1;
    address user2;
    address validFeeCollector;
    address zeroAddress = address(0);
    
    uint256 constant INITIAL_BALANCE = 1000 ether;
    uint256 constant FEE_AMOUNT_A = 10 ether;
    uint256 constant FEE_AMOUNT_B = 5 ether;
    
    // OrderBook deployment parameters
    uint96 constant SIZE_PRECISION = 10 ** 10;
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint96 constant SPREAD = 30;

    function setUp() public {
        owner = address(this);
        user1 = makeAddr("user1");
        user2 = makeAddr("user2");
        validFeeCollector = makeAddr("validFeeCollector");
        
        // Deploy tokens
        tokenA = new MintableERC20("TokenA", "TKA");
        tokenB = new MintableERC20("TokenB", "TKB");
        
        // Deploy and initialize contracts
        _setupContracts();
        
        // Mint tokens for testing
        tokenA.mint(address(marginAccount), INITIAL_BALANCE);
        tokenB.mint(address(marginAccount), INITIAL_BALANCE);
    }
    
    function _setupContracts() internal {
        // Deploy Router
        Router routerImpl = new Router();
        router = Router(payable(address(new ERC1967Proxy(address(routerImpl), ""))));
        
        // Deploy MarginAccount with valid feeCollector initially
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(owner, address(router), validFeeCollector, address(0));
        
        // Deploy OrderBook
        OrderBook orderBookImpl = new OrderBook();
        orderBook = OrderBook(address(new ERC1967Proxy(address(orderBookImpl), "")));
        
        // Initialize Router
        router.initialize(owner, address(marginAccount), address(orderBookImpl), address(0), address(0));
        
        // Initialize OrderBook
        orderBook.initialize(
            owner,
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            tokenA.decimals(),
            address(tokenB),
            tokenB.decimals(),
            address(marginAccount),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            100, // takerFeeBps (1%)
            50,  // makerFeeBps (0.5%)
            address(0), // vault
            SPREAD,
            address(0) // forwarder
        );
        
        // Register orderBook as verified market (must be called by router)
        vm.prank(address(router));
        marginAccount.updateMarkets(address(orderBook));
    }

    /**
     * @dev Test Case 1: Verify that setFeeCollector allows zero address
     * This tests the core of the alleged bug
     */
    function test_SetFeeCollectorAllowsZeroAddress() public {
        // Verify initial state - feeCollector should be validFeeCollector
        // Note: MarginAccount doesn't have a public getter for feeCollector
        // We'll verify through behavior in subsequent tests
        
        // Attempt to set feeCollector to zero address
        // This should succeed if the bug exists (no validation against zero address)
        marginAccount.setFeeCollector(zeroAddress);
        
        // If we reach here without reverting, the bug is confirmed
        // The function allows setting feeCollector to zero address
        assertTrue(true, "setFeeCollector allows zero address - BUG CONFIRMED");
    }

    /**
     * @dev Test Case 2: Verify that setFeeCollector reverts when setting same address
     * This tests the existing validation logic
     */
    function test_SetFeeCollectorRevertsOnSameAddress() public {
        // Try to set feeCollector to the same address (should revert)
        vm.expectRevert(MarginAccountErrors.FeeCollectorNotChanged.selector);
        marginAccount.setFeeCollector(validFeeCollector);
    }

    /**
     * @dev Test Case 3: Test creditFee function with zero address feeCollector
     * This verifies that fees can be credited to zero address
     */
    function test_CreditFeeWithZeroAddressFeeCollector() public {
        // First set feeCollector to zero address
        marginAccount.setFeeCollector(zeroAddress);
        
        // Get initial balance of zero address for both tokens
        uint256 initialBalanceA = marginAccount.getBalance(zeroAddress, address(tokenA));
        uint256 initialBalanceB = marginAccount.getBalance(zeroAddress, address(tokenB));
        
        // Credit fees to the zero address feeCollector
        vm.prank(address(orderBook)); // Only verified markets can call creditFee
        marginAccount.creditFee(address(tokenA), FEE_AMOUNT_A, address(tokenB), FEE_AMOUNT_B);
        
        // Verify that fees were credited to zero address
        uint256 finalBalanceA = marginAccount.getBalance(zeroAddress, address(tokenA));
        uint256 finalBalanceB = marginAccount.getBalance(zeroAddress, address(tokenB));
        
        assertEq(finalBalanceA, initialBalanceA + FEE_AMOUNT_A, "TokenA fees should be credited to zero address");
        assertEq(finalBalanceB, initialBalanceB + FEE_AMOUNT_B, "TokenB fees should be credited to zero address");
        
        // This confirms that fees can accumulate at zero address
        assertTrue(finalBalanceA > 0 || finalBalanceB > 0, "Fees accumulated at zero address - FUNDS STUCK");
    }

    /**
     * @dev Test Case 4: Verify that funds at zero address are unclaimable
     * This demonstrates the impact of the bug
     */
    function test_ZeroAddressFeesAreUnclaimable() public {
        // Set feeCollector to zero address and credit some fees
        marginAccount.setFeeCollector(zeroAddress);
        
        vm.prank(address(orderBook));
        marginAccount.creditFee(address(tokenA), FEE_AMOUNT_A, address(tokenB), FEE_AMOUNT_B);
        
        // Verify fees are at zero address
        uint256 balanceA = marginAccount.getBalance(zeroAddress, address(tokenA));
        uint256 balanceB = marginAccount.getBalance(zeroAddress, address(tokenB));
        
        assertTrue(balanceA > 0, "TokenA fees should be at zero address");
        assertTrue(balanceB > 0, "TokenB fees should be at zero address");
        
        // Attempt to withdraw from zero address (should fail)
        // Zero address cannot initiate transactions, so these funds are permanently stuck
        
        // Try to withdraw as zero address (this will fail because zero address can't sign transactions)
        vm.expectRevert(); // Any revert is expected since zero address can't execute transactions
        vm.prank(zeroAddress);
        marginAccount.withdraw(balanceA, address(tokenA));
        
        // Verify funds are still stuck at zero address
        assertEq(marginAccount.getBalance(zeroAddress, address(tokenA)), balanceA, "Funds remain stuck at zero address");
        assertEq(marginAccount.getBalance(zeroAddress, address(tokenB)), balanceB, "Funds remain stuck at zero address");
    }

    /**
     * @dev Test Case 5: Compare behavior with valid feeCollector
     * This shows normal operation vs the bug scenario
     */
    function test_ValidFeeCollectorWorksCorrectly() public {
        // Keep feeCollector as validFeeCollector (initial state)
        
        // Credit fees to valid feeCollector
        vm.prank(address(orderBook));
        marginAccount.creditFee(address(tokenA), FEE_AMOUNT_A, address(tokenB), FEE_AMOUNT_B);
        
        // Verify fees are credited to valid feeCollector
        uint256 balanceA = marginAccount.getBalance(validFeeCollector, address(tokenA));
        uint256 balanceB = marginAccount.getBalance(validFeeCollector, address(tokenB));
        
        assertEq(balanceA, FEE_AMOUNT_A, "TokenA fees should be credited to valid feeCollector");
        assertEq(balanceB, FEE_AMOUNT_B, "TokenB fees should be credited to valid feeCollector");
        
        // Valid feeCollector can withdraw fees (demonstrating claimable funds)
        vm.startPrank(validFeeCollector);
        marginAccount.withdraw(balanceA, address(tokenA));
        marginAccount.withdraw(balanceB, address(tokenB));
        vm.stopPrank();
        
        // Verify successful withdrawal
        assertEq(marginAccount.getBalance(validFeeCollector, address(tokenA)), 0, "TokenA fees should be withdrawn");
        assertEq(marginAccount.getBalance(validFeeCollector, address(tokenB)), 0, "TokenB fees should be withdrawn");
        
        // Verify tokens were transferred to feeCollector
        assertEq(tokenA.balanceOf(validFeeCollector), FEE_AMOUNT_A, "TokenA should be in feeCollector wallet");
        assertEq(tokenB.balanceOf(validFeeCollector), FEE_AMOUNT_B, "TokenB should be in feeCollector wallet");
    }

    /**
     * @dev Test Case 6: Test multiple fee accumulations at zero address
     * This demonstrates the cumulative impact of the bug
     */
    function test_MultipleFeeAccumulationsAtZeroAddress() public {
        // Set feeCollector to zero address
        marginAccount.setFeeCollector(zeroAddress);
        
        uint256 iterations = 5;
        uint256 expectedTotalA = 0;
        uint256 expectedTotalB = 0;
        
        // Credit fees multiple times
        for (uint256 i = 0; i < iterations; i++) {
            vm.prank(address(orderBook));
            marginAccount.creditFee(address(tokenA), FEE_AMOUNT_A, address(tokenB), FEE_AMOUNT_B);
            
            expectedTotalA += FEE_AMOUNT_A;
            expectedTotalB += FEE_AMOUNT_B;
        }
        
        // Verify cumulative fees at zero address
        uint256 totalBalanceA = marginAccount.getBalance(zeroAddress, address(tokenA));
        uint256 totalBalanceB = marginAccount.getBalance(zeroAddress, address(tokenB));
        
        assertEq(totalBalanceA, expectedTotalA, "Cumulative TokenA fees should be at zero address");
        assertEq(totalBalanceB, expectedTotalB, "Cumulative TokenB fees should be at zero address");
        
        // Calculate total value stuck (for impact assessment)
        uint256 totalStuckValue = totalBalanceA + totalBalanceB;
        assertTrue(totalStuckValue > 0, "Significant value is permanently stuck at zero address");
        
        // Log the stuck value for visibility
        emit log_named_uint("Total stuck value (TokenA + TokenB)", totalStuckValue);
    }

    /**
     * @dev Test Case 7: Verify owner can change feeCollector from zero to valid address
     * This tests potential recovery mechanism
     */
    function test_OwnerCanChangeFeeCollectorFromZeroToValid() public {
        // Set feeCollector to zero address
        marginAccount.setFeeCollector(zeroAddress);
        
        // Credit some fees to zero address
        vm.prank(address(orderBook));
        marginAccount.creditFee(address(tokenA), FEE_AMOUNT_A, address(tokenB), FEE_AMOUNT_B);
        
        // Verify fees are stuck at zero address
        uint256 stuckBalanceA = marginAccount.getBalance(zeroAddress, address(tokenA));
        uint256 stuckBalanceB = marginAccount.getBalance(zeroAddress, address(tokenB));
        assertTrue(stuckBalanceA > 0 && stuckBalanceB > 0, "Fees should be stuck at zero address");
        
        // Owner changes feeCollector back to valid address
        marginAccount.setFeeCollector(validFeeCollector);
        
        // Credit new fees (should go to valid feeCollector now)
        vm.prank(address(orderBook));
        marginAccount.creditFee(address(tokenA), FEE_AMOUNT_A, address(tokenB), FEE_AMOUNT_B);
        
        // Verify new fees go to valid feeCollector
        uint256 newBalanceA = marginAccount.getBalance(validFeeCollector, address(tokenA));
        uint256 newBalanceB = marginAccount.getBalance(validFeeCollector, address(tokenB));
        assertEq(newBalanceA, FEE_AMOUNT_A, "New fees should go to valid feeCollector");
        assertEq(newBalanceB, FEE_AMOUNT_B, "New fees should go to valid feeCollector");
        
        // But old fees remain stuck at zero address
        assertEq(marginAccount.getBalance(zeroAddress, address(tokenA)), stuckBalanceA, "Old fees remain stuck at zero address");
        assertEq(marginAccount.getBalance(zeroAddress, address(tokenB)), stuckBalanceB, "Old fees remain stuck at zero address");
        
        // This demonstrates that changing feeCollector doesn't recover already stuck funds
        assertTrue(stuckBalanceA > 0, "Previously stuck funds cannot be recovered");
    }
}
