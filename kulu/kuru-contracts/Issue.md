in KuruForwarder.sol  

Price can be trivially manipulated at the trigger point (no TWAP/min-size/latency)

Where: executePriceDependent reads _currentBidPrice directly from IOrderBook(req.market).bestBidAsk() right before execution.

Why:

If the book allows micro-orders, an attacker can nudge bid/ask for one block (or within the same block via MEV), trigger your meta-tx, then revert the book back — classic spot oracle manipulation.

No min-size, no TWAP window, no “recent trade” guard, no heartbeat.

Impact: Forced executions at manipulated prices; griefing or value extraction.