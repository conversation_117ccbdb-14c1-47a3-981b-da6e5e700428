in KuruForwarder.sol  

Where: execute, executeMarginAccountRequest, executePriceDependent

require(msg.value >= req.value, ...);
... call{value: req.value}(...);


Why: Any msg.value > req.value remains stuck in the forwarder; there’s no withdraw. This lets careless relayers strand ETH.

Impact: Funds stranded in forwarder (not user-stealable, but operationally bad and could grow).

Mitigations:

Enforce exact match: require(msg.value == req.value, ...).

Or refund the excess after the call (be careful to avoid reentrancy; do it last, using Address.sendValue/call).